{"bot_name": "Customer Service Bot", "bot_type": "customer_service", "description": "Generic customer service bot configuration for testing various conversation flows", "test_scenarios": [{"name": "Welcome Flow", "description": "Test bot welcome and initial responses", "expected_keywords": ["welcome", "hello", "menu", "help", "options"], "messages": [{"text": "hi", "description": "Test welcome trigger", "wait_time": 2}, {"text": "hello", "description": "Test welcome variant", "wait_time": 2}, {"text": "help", "description": "Test help command", "wait_time": 2}]}, {"name": "Menu Navigation", "description": "Test main menu and navigation", "expected_keywords": ["menu", "option", "select", "choose", "category"], "messages": [{"text": "menu", "description": "Test main menu", "wait_time": 3}, {"text": "back", "description": "Test back navigation", "wait_time": 2}, {"text": "main menu", "description": "Test return to main", "wait_time": 2}]}, {"name": "Support Flow", "description": "Test customer support conversation flow", "expected_keywords": ["support", "agent", "help", "contact", "assistance"], "messages": [{"text": "I need support", "description": "Request support", "wait_time": 3}, {"text": "talk to agent", "description": "Request human agent", "wait_time": 3}, {"text": "customer service", "description": "Request customer service", "wait_time": 3}]}, {"name": "Erro<PERSON>", "description": "Test bot error handling and fallbacks", "expected_keywords": ["sorry", "understand", "help", "try again", "invalid"], "messages": [{"text": "invalid_command_xyz", "description": "Test invalid input", "wait_time": 2}, {"text": "random gibberish text", "description": "Test unrecognized input", "wait_time": 2}, {"text": "reset", "description": "Test bot reset", "wait_time": 2}]}, {"name": "Information Requests", "description": "Test information and FAQ responses", "expected_keywords": ["hours", "location", "contact", "info", "about"], "messages": [{"text": "what are your hours", "description": "Test hours inquiry", "wait_time": 3}, {"text": "contact information", "description": "Test contact info request", "wait_time": 3}, {"text": "about your company", "description": "Test company info request", "wait_time": 3}]}], "expected_responses": {"welcome_keywords": ["welcome", "hello", "menu", "help", "options"], "menu_keywords": ["option", "select", "choose", "menu", "category"], "support_keywords": ["support", "agent", "help", "contact", "assistance"], "error_keywords": ["sorry", "understand", "help", "try again", "invalid"], "info_keywords": ["hours", "location", "contact", "info", "about"]}, "test_settings": {"default_wait_time": 2, "response_timeout": 10, "max_conversation_depth": 20, "retry_failed_messages": true, "log_detailed_responses": true}}