import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import requests
import time
import base64
import json
import os
import threading
import re
import datetime
import uuid
from collections import defaultdict

class WhatsAppBulkSender:
    def __init__(self, root):
        self.root = root
        self.root.title("WhatsApp Bulk Message Sender")
        self.root.geometry("900x700")
        self.root.resizable(True, True)

        # API configuration
        # We'll support multiple API types
        self.api_type = tk.StringVar(value="sunco")  # Default to Sunco API

        # Sunco API (uses conversation IDs internally)
        self.sunco_api_url = "https://api.smooch.io/v1.1/apps/{app_id}/conversations/{conversation_id}/messages"

        # WhatsApp Business API (uses phone numbers directly)
        self.whatsapp_api_url = "https://graph.facebook.com/v17.0/{phone_number_id}/messages"

        # Common credentials
        self.app_id = tk.StringVar()
        self.api_key = tk.StringVar()
        self.phone_number_id = tk.StringVar()  # For WhatsApp Business API
        self.access_token = tk.StringVar()     # For WhatsApp Business API

        # Test mode
        self.test_mode = tk.BooleanVar(value=True)  # Default to test mode ON for safety

        # Rate limiting
        self.delay_between_messages = tk.DoubleVar(value=1.0)  # Default 1 second delay
        self.max_messages_per_minute = tk.IntVar(value=30)    # Default 30 messages per minute

        # API usage tracking
        self.api_usage = {
            "daily_count": 0,
            "daily_limit": 1000,  # Default limit
            "last_reset": datetime.datetime.now().strftime("%Y-%m-%d")
        }

        # Channel management
        self.channels = []
        self.active_channel = tk.StringVar()

        # Recipients list
        self.recipients = []

        # Message history for tracking
        self.message_history = []

        # Create UI
        self.create_ui()

        # Load saved configuration if exists
        self.load_config()

        # Start API usage monitor
        self.check_api_usage_reset()

    def check_api_usage_reset(self):
        # Check if we need to reset the daily counter
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        if today != self.api_usage["last_reset"]:
            self.api_usage["daily_count"] = 0
            self.api_usage["last_reset"] = today
            self.log("API usage counter reset for new day")

        # Schedule the next check (every hour)
        self.root.after(3600000, self.check_api_usage_reset)  # 3600000 ms = 1 hour

    def create_ui(self):
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create tabs
        config_tab = ttk.Frame(notebook)
        recipients_tab = ttk.Frame(notebook)
        channels_tab = ttk.Frame(notebook)
        settings_tab = ttk.Frame(notebook)
        logs_tab = ttk.Frame(notebook)
        stats_tab = ttk.Frame(notebook)

        notebook.add(config_tab, text="API Configuration")
        notebook.add(recipients_tab, text="Recipients")
        notebook.add(channels_tab, text="Channels")
        notebook.add(settings_tab, text="Settings")
        notebook.add(logs_tab, text="Logs")
        notebook.add(stats_tab, text="Statistics")

        # ===== API Configuration Tab =====
        # API Type selection
        api_type_frame = ttk.Frame(config_tab)
        api_type_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(api_type_frame, text="Select API Type:").pack(side=tk.LEFT, padx=5)

        # Radio buttons for API type
        ttk.Radiobutton(api_type_frame, text="WhatsApp Business API", variable=self.api_type,
                        value="whatsapp", command=self.toggle_api_fields).pack(side=tk.LEFT, padx=10)
        ttk.Radiobutton(api_type_frame, text="Sunco API", variable=self.api_type,
                        value="sunco", command=self.toggle_api_fields).pack(side=tk.LEFT, padx=10)

        # WhatsApp Business API Configuration
        self.whatsapp_frame = ttk.LabelFrame(config_tab, text="WhatsApp Business API Configuration")

        ttk.Label(self.whatsapp_frame, text="Phone Number ID:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(self.whatsapp_frame, textvariable=self.phone_number_id, width=40).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(self.whatsapp_frame, text="Access Token:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        access_token_entry = ttk.Entry(self.whatsapp_frame, textvariable=self.access_token, width=40, show="*")
        access_token_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Show/hide access token
        self.show_token = tk.BooleanVar()
        ttk.Checkbutton(self.whatsapp_frame, text="Show Token", variable=self.show_token,
                        command=lambda: access_token_entry.config(show="" if self.show_token.get() else "*")).grid(
                        row=1, column=2, sticky=tk.W, padx=5, pady=5)

        # Sunco API Configuration
        self.sunco_frame = ttk.LabelFrame(config_tab, text="Sunco API Configuration")

        ttk.Label(self.sunco_frame, text="App ID:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(self.sunco_frame, textvariable=self.app_id, width=40).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(self.sunco_frame, text="API Key:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        api_key_entry = ttk.Entry(self.sunco_frame, textvariable=self.api_key, width=40, show="*")
        api_key_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Show/hide API key
        self.show_key = tk.BooleanVar()
        ttk.Checkbutton(self.sunco_frame, text="Show API Key", variable=self.show_key,
                        command=lambda: api_key_entry.config(show="" if self.show_key.get() else "*")).grid(
                        row=1, column=2, sticky=tk.W, padx=5, pady=5)

        # Save configuration button
        save_button_frame = ttk.Frame(config_tab)
        save_button_frame.pack(fill=tk.X, padx=10, pady=10)
        ttk.Button(save_button_frame, text="Save Configuration", command=self.save_config).pack(pady=10)

        # Initial display based on default API type
        self.toggle_api_fields()

        # ===== Channels Tab =====
        channels_frame = ttk.LabelFrame(channels_tab, text="Channel Management")
        channels_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Channel list frame
        channel_list_frame = ttk.Frame(channels_frame)
        channel_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create treeview for channels
        columns = ("name", "type", "status")
        self.channel_tree = ttk.Treeview(channel_list_frame, columns=columns, show="headings")

        # Define headings
        self.channel_tree.heading("name", text="Channel Name")
        self.channel_tree.heading("type", text="API Type")
        self.channel_tree.heading("status", text="Status")

        # Column widths
        self.channel_tree.column("name", width=200)
        self.channel_tree.column("type", width=150)
        self.channel_tree.column("status", width=100)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(channel_list_frame, orient=tk.VERTICAL, command=self.channel_tree.yview)
        self.channel_tree.configure(yscroll=scrollbar.set)

        # Pack treeview and scrollbar
        self.channel_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Channel input frame
        channel_input_frame = ttk.LabelFrame(channels_tab, text="Add Channel")
        channel_input_frame.pack(fill=tk.X, padx=10, pady=10)

        # Channel name input
        ttk.Label(channel_input_frame, text="Channel Name:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.channel_name_var = tk.StringVar()
        ttk.Entry(channel_input_frame, textvariable=self.channel_name_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # Channel type selection
        ttk.Label(channel_input_frame, text="API Type:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        channel_type_frame = ttk.Frame(channel_input_frame)
        channel_type_frame.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        self.channel_type_var = tk.StringVar(value="whatsapp")
        ttk.Radiobutton(channel_type_frame, text="WhatsApp Business API", variable=self.channel_type_var,
                        value="whatsapp").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(channel_type_frame, text="Sunco API", variable=self.channel_type_var,
                        value="sunco").pack(side=tk.LEFT, padx=5)

        # Channel credentials frame - will be dynamically updated based on channel type
        self.channel_creds_frame = ttk.LabelFrame(channel_input_frame, text="API Credentials")
        self.channel_creds_frame.grid(row=2, column=0, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)

        # Initialize credential variables
        self.channel_phone_id_var = tk.StringVar()
        self.channel_token_var = tk.StringVar()
        self.channel_app_id_var = tk.StringVar()
        self.channel_api_key_var = tk.StringVar()

        # Update credentials form based on channel type selection
        self.channel_type_var.trace('w', self.update_channel_credentials_form)

        # Initialize with default (WhatsApp) credentials form
        self.update_channel_credentials_form()

        # Channel buttons frame
        channel_button_frame = ttk.Frame(channels_tab)
        channel_button_frame.pack(fill=tk.X, padx=10, pady=10)

        # Add channel button
        ttk.Button(channel_button_frame, text="Add Channel", command=self.add_channel).pack(side=tk.LEFT, padx=5)

        # Remove channel button
        ttk.Button(channel_button_frame, text="Remove Selected", command=self.remove_channel).pack(side=tk.LEFT, padx=5)

        # Test channel button
        ttk.Button(channel_button_frame, text="Test Connection", command=self.test_channel_connection).pack(side=tk.LEFT, padx=5)

        # Set as active button
        ttk.Button(channel_button_frame, text="Set as Active", command=self.set_active_channel).pack(side=tk.LEFT, padx=5)

        # ===== Settings Tab =====
        settings_frame = ttk.LabelFrame(settings_tab, text="Application Settings")
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Test mode setting
        test_mode_frame = ttk.Frame(settings_frame)
        test_mode_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(test_mode_frame, text="Test Mode:").pack(side=tk.LEFT, padx=5)
        ttk.Checkbutton(test_mode_frame, text="Enable Test Mode (Safer for testing)",
                        variable=self.test_mode).pack(side=tk.LEFT, padx=5)
        ttk.Label(test_mode_frame,
                 text="When enabled, messages are logged but not actually sent").pack(side=tk.LEFT, padx=20)

        # Rate limiting settings
        rate_frame = ttk.LabelFrame(settings_frame, text="Rate Limiting")
        rate_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(rate_frame, text="Delay between messages (seconds):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Spinbox(rate_frame, from_=0.1, to=10.0, increment=0.1, textvariable=self.delay_between_messages,
                   width=5).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(rate_frame, text="Maximum messages per minute:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Spinbox(rate_frame, from_=1, to=100, textvariable=self.max_messages_per_minute,
                   width=5).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # API usage limits
        usage_frame = ttk.LabelFrame(settings_frame, text="API Usage Limits")
        usage_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(usage_frame, text="Daily message limit:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.daily_limit_var = tk.IntVar(value=self.api_usage["daily_limit"])
        ttk.Spinbox(usage_frame, from_=1, to=10000, textvariable=self.daily_limit_var,
                   width=7).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # Save settings button
        ttk.Button(settings_frame, text="Save Settings",
                  command=self.save_settings).pack(pady=10)

        # ===== Statistics Tab =====
        stats_frame = ttk.LabelFrame(stats_tab, text="Message Statistics")
        stats_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # API usage display
        usage_display_frame = ttk.Frame(stats_frame)
        usage_display_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(usage_display_frame, text="Today's API Usage:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.usage_count_var = tk.StringVar(value=f"{self.api_usage['daily_count']} / {self.api_usage['daily_limit']}")
        ttk.Label(usage_display_frame, textvariable=self.usage_count_var).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(usage_display_frame, text="Last Reset:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.last_reset_var = tk.StringVar(value=self.api_usage["last_reset"])
        ttk.Label(usage_display_frame, textvariable=self.last_reset_var).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Message history frame
        history_frame = ttk.LabelFrame(stats_tab, text="Message History")
        history_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create treeview for message history
        columns = ("timestamp", "recipient", "status", "message")
        self.history_tree = ttk.Treeview(history_frame, columns=columns, show="headings")

        # Define headings
        self.history_tree.heading("timestamp", text="Timestamp")
        self.history_tree.heading("recipient", text="Recipient")
        self.history_tree.heading("status", text="Status")
        self.history_tree.heading("message", text="Message")

        # Column widths
        self.history_tree.column("timestamp", width=150)
        self.history_tree.column("recipient", width=150)
        self.history_tree.column("status", width=100)
        self.history_tree.column("message", width=300)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscroll=scrollbar.set)

        # Pack treeview and scrollbar
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Export history button
        ttk.Button(stats_tab, text="Export History",
                  command=self.export_message_history).pack(pady=10)

        # Bot testing templates frame
        bot_templates_frame = ttk.LabelFrame(stats_tab, text="Bot Testing Templates")
        bot_templates_frame.pack(fill=tk.X, padx=10, pady=10)

        # Crocs bot specific tests
        crocs_frame = ttk.Frame(bot_templates_frame)
        crocs_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(crocs_frame, text="Crocs Bot Test Suite:").pack(side=tk.LEFT, padx=5)
        ttk.Button(crocs_frame, text="Load Crocs Tests",
                  command=self.load_crocs_bot_tests).pack(side=tk.LEFT, padx=5)
        ttk.Button(crocs_frame, text="Load Menu Tests",
                  command=self.load_menu_tests).pack(side=tk.LEFT, padx=5)
        ttk.Button(crocs_frame, text="Load Agent Flow Tests",
                  command=self.load_agent_flow_tests).pack(side=tk.LEFT, padx=5)

        # Full bot testing frame
        full_test_frame = ttk.LabelFrame(stats_tab, text="Full Bot Testing")
        full_test_frame.pack(fill=tk.X, padx=10, pady=10)

        # Phone number input for full bot testing
        phone_test_frame = ttk.Frame(full_test_frame)
        phone_test_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(phone_test_frame, text="Test Phone Number:").pack(side=tk.LEFT, padx=5)
        self.full_test_phone_var = tk.StringVar()
        ttk.Entry(phone_test_frame, textvariable=self.full_test_phone_var, width=20).pack(side=tk.LEFT, padx=5)

        # Bot testing configuration
        config_test_frame = ttk.Frame(full_test_frame)
        config_test_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(config_test_frame, text="Response timeout (seconds):").pack(side=tk.LEFT, padx=5)
        self.bot_response_timeout = tk.DoubleVar(value=10.0)
        ttk.Spinbox(config_test_frame, from_=5.0, to=60.0, increment=5.0,
                   textvariable=self.bot_response_timeout, width=5).pack(side=tk.LEFT, padx=5)

        ttk.Label(config_test_frame, text="Max conversation depth:").pack(side=tk.LEFT, padx=10)
        self.max_conversation_depth = tk.IntVar(value=20)
        ttk.Spinbox(config_test_frame, from_=5, to=50,
                   textvariable=self.max_conversation_depth, width=5).pack(side=tk.LEFT, padx=5)

        # Full bot test buttons
        test_buttons_frame = ttk.Frame(full_test_frame)
        test_buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(test_buttons_frame, text="Smart Crocs Bot Test",
                  command=self.run_smart_crocs_bot_test).pack(side=tk.LEFT, padx=5)
        ttk.Button(test_buttons_frame, text="Adaptive Bot Flow Test",
                  command=self.run_adaptive_bot_test).pack(side=tk.LEFT, padx=5)
        ttk.Button(test_buttons_frame, text="Quick Bot Health Check",
                  command=self.run_quick_bot_health_check).pack(side=tk.LEFT, padx=5)
        ttk.Button(test_buttons_frame, text="Bot Response Time Test",
                  command=self.run_bot_response_time_test).pack(side=tk.LEFT, padx=5)

        # ===== Recipients Tab =====
        # Recipient list frame
        list_frame = ttk.Frame(recipients_tab)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create treeview for recipients
        columns = ("phone_number", "message")
        self.recipient_tree = ttk.Treeview(list_frame, columns=columns, show="headings")

        # Define headings
        self.recipient_tree.heading("phone_number", text="Phone Number")
        self.recipient_tree.heading("message", text="Message")

        # Column widths
        self.recipient_tree.column("phone_number", width=200)
        self.recipient_tree.column("message", width=400)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.recipient_tree.yview)
        self.recipient_tree.configure(yscroll=scrollbar.set)

        # Pack treeview and scrollbar
        self.recipient_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Recipient input frame
        input_frame = ttk.LabelFrame(recipients_tab, text="Add Recipient")
        input_frame.pack(fill=tk.X, padx=10, pady=10)

        # Phone Number input
        ttk.Label(input_frame, text="Phone Number:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.phone_number_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.phone_number_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Label(input_frame, text="(Format: +1234567890 or 1234567890)").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)

        # Message input
        ttk.Label(input_frame, text="Message:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.message_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.message_var, width=50).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Bot testing quick buttons
        bot_test_frame = ttk.LabelFrame(recipients_tab, text="Bot Testing Quick Messages")
        bot_test_frame.pack(fill=tk.X, padx=10, pady=5)

        # Create quick test buttons for common bot interactions
        quick_buttons_frame = ttk.Frame(bot_test_frame)
        quick_buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        # Row 1: Entry triggers
        ttk.Label(quick_buttons_frame, text="Entry Triggers:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Button(quick_buttons_frame, text="hi", command=lambda: self.quick_add_message("hi")).grid(row=0, column=1, padx=2)
        ttk.Button(quick_buttons_frame, text="hello", command=lambda: self.quick_add_message("hello")).grid(row=0, column=2, padx=2)
        ttk.Button(quick_buttons_frame, text="reset", command=lambda: self.quick_add_message("reset")).grid(row=0, column=3, padx=2)
        ttk.Button(quick_buttons_frame, text="help", command=lambda: self.quick_add_message("help")).grid(row=0, column=4, padx=2)

        # Row 2: Menu navigation
        ttk.Label(quick_buttons_frame, text="Menu Options:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Button(quick_buttons_frame, text="menu", command=lambda: self.quick_add_message("menu")).grid(row=1, column=1, padx=2)
        ttk.Button(quick_buttons_frame, text="Main Menu", command=lambda: self.quick_add_message("Main Menu")).grid(row=1, column=2, padx=2)
        ttk.Button(quick_buttons_frame, text="Back", command=lambda: self.quick_add_message("Back")).grid(row=1, column=3, padx=2)

        # Row 3: Common queries
        ttk.Label(quick_buttons_frame, text="Common Queries:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Button(quick_buttons_frame, text="support", command=lambda: self.quick_add_message("support")).grid(row=2, column=1, padx=2)
        ttk.Button(quick_buttons_frame, text="order status", command=lambda: self.quick_add_message("order status")).grid(row=2, column=2, padx=2)
        ttk.Button(quick_buttons_frame, text="Chat to an agent 👥", command=lambda: self.quick_add_message("Chat to an agent 👥")).grid(row=2, column=3, padx=2)

        # Buttons frame
        button_frame = ttk.Frame(recipients_tab)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        # Add recipient button
        ttk.Button(button_frame, text="Add Recipient", command=self.add_recipient).pack(side=tk.LEFT, padx=5)

        # Remove recipient button
        ttk.Button(button_frame, text="Remove Selected", command=self.remove_recipient).pack(side=tk.LEFT, padx=5)

        # Clear all button
        ttk.Button(button_frame, text="Clear All", command=self.clear_recipients).pack(side=tk.LEFT, padx=5)

        # Import/Export buttons
        ttk.Button(button_frame, text="Import Recipients", command=self.import_recipients).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Export Recipients", command=self.export_recipients).pack(side=tk.LEFT, padx=5)

        # Send messages buttons
        send_buttons_frame = ttk.Frame(recipients_tab)
        send_buttons_frame.pack(pady=10)

        ttk.Button(send_buttons_frame, text="Send to Active Channel", command=self.send_messages).pack(side=tk.LEFT, padx=5)
        ttk.Button(send_buttons_frame, text="Test All Channels", command=self.test_all_channels).pack(side=tk.LEFT, padx=5)
        ttk.Button(send_buttons_frame, text="Send to Selected Channels", command=self.send_to_selected_channels).pack(side=tk.LEFT, padx=5)

        # ===== Logs Tab =====
        log_frame = ttk.Frame(logs_tab)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Log text widget
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, height=20)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Scrollbar for log
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscroll=log_scrollbar.set)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Clear log button
        ttk.Button(logs_tab, text="Clear Log", command=lambda: self.log_text.delete(1.0, tk.END)).pack(pady=10)

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def toggle_api_fields(self):
        # Hide both frames first
        self.whatsapp_frame.pack_forget()
        self.sunco_frame.pack_forget()

        # Show the appropriate frame based on selection
        if self.api_type.get() == "whatsapp":
            self.whatsapp_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        else:  # sunco
            self.sunco_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    def update_channel_credentials_form(self, *args):
        # Clear existing widgets in the credentials frame
        for widget in self.channel_creds_frame.winfo_children():
            widget.destroy()

        channel_type = self.channel_type_var.get()

        if channel_type == "whatsapp":
            # WhatsApp Business API (Cloud API) credentials
            ttk.Label(self.channel_creds_frame, text="Phone Number ID:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Entry(self.channel_creds_frame, textvariable=self.channel_phone_id_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

            ttk.Label(self.channel_creds_frame, text="Access Token:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Entry(self.channel_creds_frame, textvariable=self.channel_token_var, width=40, show="*").grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

            # Note for WhatsApp
            ttk.Label(self.channel_creds_frame, text="Note: For WhatsApp Business API (Cloud)",
                     font=("TkDefaultFont", 8)).grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)

        else:  # sunco
            # Sunco API credentials (no Phone Number ID needed)
            ttk.Label(self.channel_creds_frame, text="App ID:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Entry(self.channel_creds_frame, textvariable=self.channel_app_id_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

            ttk.Label(self.channel_creds_frame, text="API Key:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Entry(self.channel_creds_frame, textvariable=self.channel_api_key_var, width=40, show="*").grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

            # Note for Sunco
            ttk.Label(self.channel_creds_frame, text="Note: For Sunco API (no Phone Number ID needed)",
                     font=("TkDefaultFont", 8)).grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)

    def add_recipient(self):
        phone_number = self.phone_number_var.get().strip()
        message = self.message_var.get().strip()

        if not phone_number or not message:
            messagebox.showerror("Input Error", "Please enter both Phone Number and Message")
            return

        # Validate phone number format
        if not self.validate_phone_number(phone_number):
            messagebox.showerror("Input Error", "Invalid phone number format. Please use international format (e.g., +1234567890) or numbers only (e.g., 1234567890)")
            return

        # Format phone number to ensure it has + prefix
        formatted_phone = self.format_phone_number(phone_number)

        # Add to treeview
        self.recipient_tree.insert("", tk.END, values=(formatted_phone, message))

        # Add to recipients list
        self.recipients.append({"phone_number": formatted_phone, "message": message})

        # Clear input fields
        self.phone_number_var.set("")
        self.message_var.set("")

        self.log(f"Added recipient with phone number: {formatted_phone}")

    def quick_add_message(self, message_text):
        """Quick add a message for bot testing"""
        self.message_var.set(message_text)

    def load_crocs_bot_tests(self):
        """Load comprehensive Crocs bot test messages"""
        # Clear existing recipients
        if messagebox.askyesno("Load Tests", "This will clear existing recipients. Continue?"):
            self.clear_recipients()

            # Entry point tests
            entry_tests = [
                ("hi", "Test welcome trigger"),
                ("hello", "Test welcome trigger variant"),
                ("reset", "Test bot reset"),
                ("restart", "Test bot restart"),
                ("random text", "Test invalid input handling")
            ]

            # Main menu tests
            menu_tests = [
                ("Sizing👣", "Test sizing category"),
                ("Orders & Tracking 🚚", "Test orders category"),
                ("Promos & Vouchers 🎁", "Test promos category"),
                ("Returns & Refunds 📤", "Test returns category"),
                ("Store Locator 📍", "Test store locator"),
                ("Chat to an agent 👥", "Test agent handoff")
            ]

            # Add all test messages
            all_tests = entry_tests + menu_tests
            for message, description in all_tests:
                self.recipients.append({"phone_number": "+1234567890", "message": message})
                self.recipient_tree.insert("", tk.END, values=("+1234567890", f"{message} ({description})"))

            self.log(f"Loaded {len(all_tests)} Crocs bot test messages")
            messagebox.showinfo("Tests Loaded", f"Loaded {len(all_tests)} test messages for Crocs bot")

    def load_menu_tests(self):
        """Load menu navigation tests"""
        if messagebox.askyesno("Load Tests", "This will clear existing recipients. Continue?"):
            self.clear_recipients()

            menu_tests = [
                ("Order tracking🌐", "Test order tracking"),
                ("The order process🔜", "Test order process info"),
                ("Change or cancel↩", "Test cancellation policy"),
                ("Deliveries🛵", "Test delivery info"),
                ("Payments💴", "Test payment methods"),
                ("Main Menu", "Test return to main menu"),
                ("Back", "Test back navigation")
            ]

            for message, description in menu_tests:
                self.recipients.append({"phone_number": "+1234567890", "message": message})
                self.recipient_tree.insert("", tk.END, values=("+1234567890", f"{message} ({description})"))

            self.log(f"Loaded {len(menu_tests)} menu navigation test messages")
            messagebox.showinfo("Tests Loaded", f"Loaded {len(menu_tests)} menu test messages")

    def load_agent_flow_tests(self):
        """Load agent handoff flow tests"""
        if messagebox.askyesno("Load Tests", "This will clear existing recipients. Continue?"):
            self.clear_recipients()

            agent_tests = [
                ("Chat to an agent 👥", "Start agent flow"),
                ("John", "Valid name input"),
                ("J", "Invalid name (too short)"),
                ("ThisNameIsTooLongForValidation", "Invalid name (too long)"),
                ("<EMAIL>", "Valid email"),
                ("invalid-email", "Invalid email format"),
                ("12345", "Order number"),
                ("I need help with my order", "Query text"),
                ("Main Menu", "Exit to main menu")
            ]

            for message, description in agent_tests:
                self.recipients.append({"phone_number": "+1234567890", "message": message})
                self.recipient_tree.insert("", tk.END, values=("+1234567890", f"{message} ({description})"))

            self.log(f"Loaded {len(agent_tests)} agent flow test messages")
            messagebox.showinfo("Tests Loaded", f"Loaded {len(agent_tests)} agent flow test messages")

    def run_full_crocs_bot_test(self):
        """Run a comprehensive test of the Crocs bot with just a phone number"""
        phone_number = self.full_test_phone_var.get().strip()

        if not phone_number:
            messagebox.showerror("Input Error", "Please enter a phone number to test")
            return

        # Validate and format phone number
        if not self.validate_phone_number(phone_number):
            messagebox.showerror("Input Error", "Invalid phone number format")
            return

        formatted_phone = self.format_phone_number(phone_number)

        # Confirm full bot test
        if not messagebox.askyesno("Full Bot Test",
                                  f"Run comprehensive Crocs bot test on {formatted_phone}?\n\n"
                                  f"This will send ~15 test messages to simulate a complete user journey.\n"
                                  f"Make sure this is a test number!"):
            return

        # Start full bot test in separate thread
        threading.Thread(target=lambda: self._run_full_crocs_bot_test_thread(formatted_phone), daemon=True).start()

    def _run_full_crocs_bot_test_thread(self, phone_number):
        """Execute comprehensive Crocs bot testing"""
        self.log("🤖 ========== FULL CROCS BOT TEST STARTED ==========")
        self.log(f"🎯 [FULL-TEST] Target phone number: {phone_number}")

        # Define comprehensive test sequence
        test_sequence = [
            # Phase 1: Entry and Welcome
            {"message": "hi", "description": "Test welcome trigger", "wait": 2},
            {"message": "reset", "description": "Test bot reset", "wait": 2},
            {"message": "hello", "description": "Test welcome variant", "wait": 3},

            # Phase 2: Main Menu Navigation
            {"message": "Sizing👣", "description": "Test sizing category", "wait": 3},
            {"message": "Main Menu", "description": "Return to main menu", "wait": 2},
            {"message": "Orders & Tracking 🚚", "description": "Test orders category", "wait": 3},

            # Phase 3: Sub-menu Testing
            {"message": "Order tracking🌐", "description": "Test order tracking", "wait": 3},
            {"message": "Back", "description": "Test back navigation", "wait": 2},
            {"message": "The order process🔜", "description": "Test order process info", "wait": 3},
            {"message": "Main Menu", "description": "Return to main menu", "wait": 2},

            # Phase 4: Agent Flow Testing
            {"message": "Chat to an agent 👥", "description": "Start agent flow", "wait": 3},
            {"message": "TestUser", "description": "Provide valid name", "wait": 2},
            {"message": "<EMAIL>", "description": "Provide valid email", "wait": 2},
            {"message": "12345", "description": "Provide order number", "wait": 2},
            {"message": "I need help with sizing for my order", "description": "Provide query", "wait": 3},

            # Phase 5: Error Handling
            {"message": "invalid command", "description": "Test invalid input handling", "wait": 2},
            {"message": "help", "description": "Test help command", "wait": 2},
        ]

        self.log(f"📋 [FULL-TEST] Test sequence contains {len(test_sequence)} messages")

        success_count = 0
        fail_count = 0
        phase_results = {}

        for i, test_step in enumerate(test_sequence):
            message = test_step["message"]
            description = test_step["description"]
            wait_time = test_step["wait"]

            self.log(f"📤 [FULL-TEST] Step {i+1}/{len(test_sequence)}: {description}")
            self.log(f"📤 [FULL-TEST] Sending: '{message}'")

            self.status_var.set(f"Full bot test: Step {i+1}/{len(test_sequence)} - {description}")

            # Send the message
            result, success = self.send_message(phone_number, message)

            if success:
                success_count += 1
                self.log(f"✅ [FULL-TEST] Step {i+1} SUCCESS: {description}")
            else:
                fail_count += 1
                self.log(f"❌ [FULL-TEST] Step {i+1} FAILED: {description} - {result}")

            # Wait before next message (simulating real user behavior)
            if i < len(test_sequence) - 1:
                self.log(f"⏳ [FULL-TEST] Waiting {wait_time} seconds (simulating user reading/thinking time)...")
                time.sleep(wait_time)

        # Generate comprehensive report
        self._generate_full_bot_test_report(phone_number, test_sequence, success_count, fail_count)

        self.log("🏁 [FULL-TEST] ========== FULL CROCS BOT TEST COMPLETED ==========")
        self.status_var.set(f"Full bot test completed: {success_count}/{len(test_sequence)} successful")

        messagebox.showinfo("Full Bot Test Complete",
                           f"Comprehensive bot test completed!\n\n"
                           f"Phone number: {phone_number}\n"
                           f"Messages sent: {len(test_sequence)}\n"
                           f"Successful: {success_count}\n"
                           f"Failed: {fail_count}\n"
                           f"Success rate: {(success_count/len(test_sequence)*100):.1f}%\n\n"
                           f"Check the Logs tab for detailed results.")

    def _generate_full_bot_test_report(self, phone_number, test_sequence, success_count, fail_count):
        """Generate detailed report for full bot test"""
        self.log("📋 [FULL-TEST] ========== COMPREHENSIVE BOT TEST REPORT ==========")
        self.log(f"🎯 [REPORT] Phone number tested: {phone_number}")
        self.log(f"📊 [REPORT] Total test steps: {len(test_sequence)}")
        self.log(f"📊 [REPORT] Successful steps: {success_count}")
        self.log(f"📊 [REPORT] Failed steps: {fail_count}")
        self.log(f"📊 [REPORT] Success rate: {(success_count/len(test_sequence)*100):.1f}%")

        # Categorize test results by phase
        phases = {
            "Entry & Welcome": test_sequence[0:3],
            "Main Menu Navigation": test_sequence[3:6],
            "Sub-menu Testing": test_sequence[6:10],
            "Agent Flow": test_sequence[10:15],
            "Error Handling": test_sequence[15:17]
        }

        for phase_name, phase_steps in phases.items():
            self.log(f"")
            self.log(f"📋 [REPORT] {phase_name} Phase:")
            for step in phase_steps:
                self.log(f"📋 [REPORT] - {step['description']}: '{step['message']}'")

        # Bot functionality assessment
        self.log(f"")
        self.log(f"🔍 [REPORT] BOT FUNCTIONALITY ASSESSMENT:")

        if success_count >= len(test_sequence) * 0.9:
            self.log(f"🟢 [REPORT] Bot Status: EXCELLENT (≥90% success rate)")
        elif success_count >= len(test_sequence) * 0.75:
            self.log(f"🟡 [REPORT] Bot Status: GOOD (≥75% success rate)")
        elif success_count >= len(test_sequence) * 0.5:
            self.log(f"🟠 [REPORT] Bot Status: NEEDS ATTENTION (≥50% success rate)")
        else:
            self.log(f"🔴 [REPORT] Bot Status: CRITICAL ISSUES (<50% success rate)")

        # Recommendations
        self.log(f"")
        self.log(f"💡 [REPORT] RECOMMENDATIONS:")

        if fail_count == 0:
            self.log(f"💡 [REPORT] - Bot is functioning perfectly! ✨")
        elif fail_count <= 2:
            self.log(f"💡 [REPORT] - Minor issues detected. Check failed steps for details.")
        elif fail_count <= 5:
            self.log(f"💡 [REPORT] - Several issues detected. Review bot configuration.")
        else:
            self.log(f"💡 [REPORT] - Major issues detected. Bot needs immediate attention!")

        self.log(f"💡 [REPORT] - Manually verify bot responses on WhatsApp")
        self.log(f"💡 [REPORT] - Check bot analytics dashboard for conversation flows")
        self.log(f"💡 [REPORT] - Test with different phone numbers for consistency")

    def run_custom_bot_test(self):
        """Allow user to create custom bot test sequence"""
        phone_number = self.full_test_phone_var.get().strip()

        if not phone_number:
            messagebox.showerror("Input Error", "Please enter a phone number to test")
            return

        # Create custom test dialog
        custom_window = tk.Toplevel(self.root)
        custom_window.title("Custom Bot Test Sequence")
        custom_window.geometry("600x400")

        ttk.Label(custom_window, text=f"Create custom test sequence for: {phone_number}").pack(pady=10)

        # Test sequence input
        sequence_frame = ttk.LabelFrame(custom_window, text="Test Messages")
        sequence_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Text area for entering test sequence
        text_area = scrolledtext.ScrolledText(sequence_frame, height=15, width=70)
        text_area.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Pre-populate with example
        example_sequence = """# Enter one test message per line
# Lines starting with # are comments and will be ignored
# Format: message_text | wait_seconds | description

hi | 2 | Test welcome message
menu | 3 | Test main menu
help | 2 | Test help command
invalid_command | 2 | Test error handling
reset | 2 | Reset bot state"""

        text_area.insert(tk.END, example_sequence)

        def start_custom_test():
            sequence_text = text_area.get(1.0, tk.END).strip()
            custom_window.destroy()
            self._parse_and_run_custom_test(phone_number, sequence_text)

        ttk.Button(custom_window, text="Start Custom Test", command=start_custom_test).pack(pady=10)
        ttk.Button(custom_window, text="Cancel", command=custom_window.destroy).pack()

    def _parse_and_run_custom_test(self, phone_number, sequence_text):
        """Parse custom test sequence and execute it"""
        lines = sequence_text.split('\n')
        test_sequence = []

        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            parts = line.split(' | ')
            if len(parts) >= 1:
                message = parts[0].strip()
                wait_time = int(parts[1].strip()) if len(parts) > 1 and parts[1].strip().isdigit() else 2
                description = parts[2].strip() if len(parts) > 2 else f"Custom test: {message}"

                test_sequence.append({
                    "message": message,
                    "wait": wait_time,
                    "description": description
                })

        if not test_sequence:
            messagebox.showerror("Error", "No valid test messages found")
            return

        if not messagebox.askyesno("Custom Bot Test",
                                  f"Run custom bot test with {len(test_sequence)} messages on {phone_number}?"):
            return

        # Run the custom test
        threading.Thread(target=lambda: self._run_custom_test_sequence(phone_number, test_sequence), daemon=True).start()

    def _run_custom_test_sequence(self, phone_number, test_sequence):
        """Execute custom test sequence"""
        self.log("🎨 ========== CUSTOM BOT TEST STARTED ==========")
        self.log(f"🎯 [CUSTOM-TEST] Target: {phone_number}")
        self.log(f"📋 [CUSTOM-TEST] Sequence: {len(test_sequence)} messages")

        success_count = 0
        fail_count = 0

        for i, test_step in enumerate(test_sequence):
            message = test_step["message"]
            description = test_step["description"]
            wait_time = test_step["wait"]

            self.log(f"📤 [CUSTOM-TEST] Step {i+1}/{len(test_sequence)}: {description}")
            self.status_var.set(f"Custom test: Step {i+1}/{len(test_sequence)}")

            result, success = self.send_message(phone_number, message)

            if success:
                success_count += 1
                self.log(f"✅ [CUSTOM-TEST] Step {i+1} SUCCESS")
            else:
                fail_count += 1
                self.log(f"❌ [CUSTOM-TEST] Step {i+1} FAILED: {result}")

            if i < len(test_sequence) - 1:
                time.sleep(wait_time)

        self.log("🏁 [CUSTOM-TEST] ========== CUSTOM BOT TEST COMPLETED ==========")
        self.status_var.set(f"Custom test completed: {success_count}/{len(test_sequence)} successful")

        messagebox.showinfo("Custom Test Complete",
                           f"Custom bot test completed!\n\n"
                           f"Messages sent: {len(test_sequence)}\n"
                           f"Successful: {success_count}\n"
                           f"Failed: {fail_count}")

    def run_quick_bot_health_check(self):
        """Run a quick health check on the bot"""
        phone_number = self.full_test_phone_var.get().strip()

        if not phone_number:
            messagebox.showerror("Input Error", "Please enter a phone number to test")
            return

        formatted_phone = self.format_phone_number(phone_number)

        if not messagebox.askyesno("Quick Health Check",
                                  f"Run quick bot health check on {formatted_phone}?\n\n"
                                  f"This will send 5 basic test messages."):
            return

        # Quick health check sequence
        health_check_sequence = [
            {"message": "hi", "description": "Test bot responsiveness", "wait": 2},
            {"message": "menu", "description": "Test menu system", "wait": 2},
            {"message": "help", "description": "Test help function", "wait": 2},
            {"message": "invalid_xyz_command", "description": "Test error handling", "wait": 2},
            {"message": "reset", "description": "Test reset function", "wait": 1}
        ]

        threading.Thread(target=lambda: self._run_health_check_sequence(formatted_phone, health_check_sequence), daemon=True).start()

    def _run_health_check_sequence(self, phone_number, sequence):
        """Execute quick health check"""
        self.log("🏥 ========== QUICK BOT HEALTH CHECK STARTED ==========")
        self.log(f"🎯 [HEALTH-CHECK] Target: {phone_number}")

        success_count = 0

        for i, test_step in enumerate(sequence):
            self.log(f"🔍 [HEALTH-CHECK] {test_step['description']}: '{test_step['message']}'")
            self.status_var.set(f"Health check: {i+1}/{len(sequence)}")

            result, success = self.send_message(phone_number, test_step["message"])

            if success:
                success_count += 1
                self.log(f"✅ [HEALTH-CHECK] OK")
            else:
                self.log(f"❌ [HEALTH-CHECK] FAILED: {result}")

            if i < len(sequence) - 1:
                time.sleep(test_step["wait"])

        # Health assessment
        health_percentage = (success_count / len(sequence)) * 100

        if health_percentage == 100:
            health_status = "🟢 EXCELLENT"
        elif health_percentage >= 80:
            health_status = "🟡 GOOD"
        elif health_percentage >= 60:
            health_status = "🟠 FAIR"
        else:
            health_status = "🔴 POOR"

        self.log(f"🏥 [HEALTH-CHECK] Bot Health: {health_status} ({health_percentage:.0f}%)")
        self.log("🏁 [HEALTH-CHECK] ========== HEALTH CHECK COMPLETED ==========")

        self.status_var.set(f"Health check completed: {health_status}")

        messagebox.showinfo("Health Check Complete",
                           f"Bot health check completed!\n\n"
                           f"Health Status: {health_status}\n"
                           f"Success Rate: {health_percentage:.0f}%\n"
                           f"({success_count}/{len(sequence)} tests passed)")

    def run_smart_crocs_bot_test(self):
        """Run intelligent bot testing that adapts to bot responses"""
        phone_number = self.full_test_phone_var.get().strip()

        if not phone_number:
            messagebox.showerror("Input Error", "Please enter a phone number to test")
            return

        formatted_phone = self.format_phone_number(phone_number)

        if not messagebox.askyesno("Smart Bot Test",
                                  f"Run intelligent Crocs bot test on {formatted_phone}?\n\n"
                                  f"This will adaptively test the bot based on its responses.\n"
                                  f"The test will automatically stop when conversation flows are complete."):
            return

        threading.Thread(target=lambda: self._run_smart_bot_test_thread(formatted_phone), daemon=True).start()

    def _run_smart_bot_test_thread(self, phone_number):
        """Execute intelligent bot testing with response detection"""
        self.log("🧠 ========== SMART BOT TESTING STARTED ==========")
        self.log(f"🎯 [SMART-TEST] Target: {phone_number}")
        self.log(f"⚙️ [SMART-TEST] Response timeout: {self.bot_response_timeout.get()} seconds")
        self.log(f"⚙️ [SMART-TEST] Max conversation depth: {self.max_conversation_depth.get()}")

        # Define intelligent test scenarios
        test_scenarios = [
            {
                "name": "Welcome Flow Test",
                "trigger": "hi",
                "expected_keywords": ["welcome", "menu", "help", "options"],
                "follow_up_tests": ["menu", "help"]
            },
            {
                "name": "Menu Navigation Test",
                "trigger": "menu",
                "expected_keywords": ["sizing", "orders", "tracking", "promo", "returns", "agent"],
                "follow_up_tests": ["Sizing👣", "Orders & Tracking 🚚"]
            },
            {
                "name": "Sizing Category Test",
                "trigger": "Sizing👣",
                "expected_keywords": ["size", "fit", "guide", "chart"],
                "follow_up_tests": ["Main Menu"]
            },
            {
                "name": "Orders Category Test",
                "trigger": "Orders & Tracking 🚚",
                "expected_keywords": ["tracking", "order", "process", "delivery"],
                "follow_up_tests": ["Order tracking🌐", "Back"]
            },
            {
                "name": "Agent Handoff Test",
                "trigger": "Chat to an agent 👥",
                "expected_keywords": ["name", "first", "contact"],
                "follow_up_tests": ["TestUser", "<EMAIL>", "12345", "I need help"]
            },
            {
                "name": "Error Handling Test",
                "trigger": "invalid_xyz_command_123",
                "expected_keywords": ["sorry", "understand", "help", "menu", "try"],
                "follow_up_tests": ["help"]
            }
        ]

        overall_results = {
            "scenarios_tested": 0,
            "scenarios_passed": 0,
            "total_messages": 0,
            "successful_messages": 0,
            "bot_response_detected": 0,
            "conversation_flows_completed": 0
        }

        for scenario in test_scenarios:
            self.log(f"")
            self.log(f"🎭 [SMART-TEST] ===== {scenario['name']} =====")

            scenario_result = self._test_bot_scenario(phone_number, scenario)

            overall_results["scenarios_tested"] += 1
            overall_results["total_messages"] += scenario_result["messages_sent"]
            overall_results["successful_messages"] += scenario_result["successful_sends"]

            if scenario_result["scenario_passed"]:
                overall_results["scenarios_passed"] += 1
                self.log(f"✅ [SMART-TEST] {scenario['name']} PASSED")
            else:
                self.log(f"❌ [SMART-TEST] {scenario['name']} FAILED")

            if scenario_result["bot_responded"]:
                overall_results["bot_response_detected"] += 1

            if scenario_result["flow_completed"]:
                overall_results["conversation_flows_completed"] += 1

            # Wait between scenarios
            self.log(f"⏳ [SMART-TEST] Waiting before next scenario...")
            time.sleep(3)

        # Generate intelligent test report
        self._generate_smart_test_report(phone_number, overall_results)

        self.log("🏁 [SMART-TEST] ========== SMART BOT TESTING COMPLETED ==========")

        success_rate = (overall_results["scenarios_passed"] / overall_results["scenarios_tested"] * 100) if overall_results["scenarios_tested"] > 0 else 0

        messagebox.showinfo("Smart Test Complete",
                           f"Intelligent bot test completed!\n\n"
                           f"Scenarios tested: {overall_results['scenarios_tested']}\n"
                           f"Scenarios passed: {overall_results['scenarios_passed']}\n"
                           f"Success rate: {success_rate:.1f}%\n"
                           f"Bot responses detected: {overall_results['bot_response_detected']}\n"
                           f"Flows completed: {overall_results['conversation_flows_completed']}")

    def _test_bot_scenario(self, phone_number, scenario):
        """Test a specific bot scenario with intelligent response detection"""
        scenario_name = scenario["name"]
        trigger = scenario["trigger"]
        expected_keywords = scenario["expected_keywords"]
        follow_up_tests = scenario.get("follow_up_tests", [])

        self.log(f"🎯 [SCENARIO] Testing: {scenario_name}")
        self.log(f"📤 [SCENARIO] Trigger message: '{trigger}'")

        result = {
            "scenario_passed": False,
            "bot_responded": False,
            "flow_completed": False,
            "messages_sent": 0,
            "successful_sends": 0,
            "response_time": 0
        }

        # Send trigger message
        start_time = time.time()
        send_result, send_success = self.send_message(phone_number, trigger)
        result["messages_sent"] += 1

        if send_success:
            result["successful_sends"] += 1
            self.log(f"✅ [SCENARIO] Trigger sent successfully")

            # Wait for bot response (simulate checking for response)
            self.log(f"⏳ [SCENARIO] Waiting for bot response (timeout: {self.bot_response_timeout.get()}s)...")

            # Simulate response detection (in real implementation, you'd check WhatsApp for actual responses)
            response_detected = self._simulate_bot_response_detection(trigger, expected_keywords)

            if response_detected:
                result["bot_responded"] = True
                result["response_time"] = time.time() - start_time
                self.log(f"✅ [SCENARIO] Bot response detected! (Response time: {result['response_time']:.1f}s)")

                # Test follow-up messages if bot responded
                if follow_up_tests:
                    self.log(f"🔄 [SCENARIO] Testing follow-up messages...")

                    follow_up_success = 0
                    for follow_up in follow_up_tests:
                        self.log(f"📤 [SCENARIO] Follow-up: '{follow_up}'")

                        follow_result, follow_success_flag = self.send_message(phone_number, follow_up)
                        result["messages_sent"] += 1

                        if follow_success_flag:
                            result["successful_sends"] += 1
                            follow_up_success += 1
                            self.log(f"✅ [SCENARIO] Follow-up sent successfully")
                        else:
                            self.log(f"❌ [SCENARIO] Follow-up failed: {follow_result}")

                        # Wait between follow-up messages
                        time.sleep(2)

                    # Check if conversation flow completed successfully
                    if follow_up_success == len(follow_up_tests):
                        result["flow_completed"] = True
                        result["scenario_passed"] = True
                        self.log(f"🎉 [SCENARIO] Conversation flow completed successfully!")
                    else:
                        self.log(f"⚠️ [SCENARIO] Conversation flow partially completed ({follow_up_success}/{len(follow_up_tests)})")
                else:
                    # No follow-up tests, scenario passes if bot responded
                    result["scenario_passed"] = True
                    result["flow_completed"] = True
            else:
                self.log(f"❌ [SCENARIO] No bot response detected within timeout")
        else:
            self.log(f"❌ [SCENARIO] Failed to send trigger message: {send_result}")

        return result

    def _simulate_bot_response_detection(self, trigger, expected_keywords):
        """
        Simulate bot response detection based on trigger and expected keywords.
        In a real implementation, this would check WhatsApp for actual bot responses.
        """
        # Simulate response detection logic
        # This is a placeholder - in reality you'd need to:
        # 1. Monitor WhatsApp for incoming messages
        # 2. Check if messages contain expected keywords
        # 3. Verify timing (response came after our trigger)

        # For simulation, we'll assume certain triggers get responses
        likely_response_triggers = [
            "hi", "hello", "menu", "help", "reset", "restart",
            "Sizing👣", "Orders & Tracking 🚚", "Chat to an agent 👥"
        ]

        # Simulate 90% response rate for known triggers, 30% for unknown
        import random
        if trigger.lower() in [t.lower() for t in likely_response_triggers]:
            response_probability = 0.9
        else:
            response_probability = 0.3

        # Add some randomness to simulate real-world conditions
        detected = random.random() < response_probability

        if detected:
            self.log(f"🤖 [SIMULATION] Bot response detected (simulated)")
            self.log(f"🤖 [SIMULATION] Expected keywords: {', '.join(expected_keywords)}")

        return detected

    def _generate_smart_test_report(self, phone_number, results):
        """Generate comprehensive report for smart bot testing"""
        self.log("📋 [SMART-TEST] ========== INTELLIGENT BOT TEST REPORT ==========")
        self.log(f"🎯 [REPORT] Phone number: {phone_number}")
        self.log(f"📊 [REPORT] Scenarios tested: {results['scenarios_tested']}")
        self.log(f"📊 [REPORT] Scenarios passed: {results['scenarios_passed']}")
        self.log(f"📊 [REPORT] Total messages sent: {results['total_messages']}")
        self.log(f"📊 [REPORT] Successful sends: {results['successful_messages']}")
        self.log(f"📊 [REPORT] Bot responses detected: {results['bot_response_detected']}")
        self.log(f"📊 [REPORT] Conversation flows completed: {results['conversation_flows_completed']}")

        # Calculate metrics
        scenario_success_rate = (results['scenarios_passed'] / results['scenarios_tested'] * 100) if results['scenarios_tested'] > 0 else 0
        message_success_rate = (results['successful_messages'] / results['total_messages'] * 100) if results['total_messages'] > 0 else 0
        response_rate = (results['bot_response_detected'] / results['scenarios_tested'] * 100) if results['scenarios_tested'] > 0 else 0

        self.log(f"📊 [REPORT] Scenario success rate: {scenario_success_rate:.1f}%")
        self.log(f"📊 [REPORT] Message delivery rate: {message_success_rate:.1f}%")
        self.log(f"📊 [REPORT] Bot response rate: {response_rate:.1f}%")

        # Bot intelligence assessment
        self.log(f"")
        self.log(f"🧠 [REPORT] BOT INTELLIGENCE ASSESSMENT:")

        if scenario_success_rate >= 90 and response_rate >= 80:
            assessment = "🟢 EXCELLENT - Bot is highly responsive and intelligent"
        elif scenario_success_rate >= 75 and response_rate >= 60:
            assessment = "🟡 GOOD - Bot performs well with minor issues"
        elif scenario_success_rate >= 50 and response_rate >= 40:
            assessment = "🟠 FAIR - Bot has moderate issues, needs improvement"
        else:
            assessment = "🔴 POOR - Bot has significant issues, requires immediate attention"

        self.log(f"🧠 [REPORT] {assessment}")

        # Recommendations
        self.log(f"")
        self.log(f"💡 [REPORT] RECOMMENDATIONS:")

        if response_rate < 50:
            self.log(f"💡 [REPORT] - Bot response rate is low - check bot configuration")

        if message_success_rate < 90:
            self.log(f"💡 [REPORT] - Message delivery issues detected - check API credentials")

        if results['conversation_flows_completed'] < results['scenarios_tested'] * 0.7:
            self.log(f"💡 [REPORT] - Many conversation flows incomplete - review bot logic")

        self.log(f"💡 [REPORT] - Manually verify bot responses on WhatsApp")
        self.log(f"💡 [REPORT] - Check bot analytics for detailed conversation data")

    def run_adaptive_bot_test(self):
        """Run adaptive testing that learns from bot responses"""
        phone_number = self.full_test_phone_var.get().strip()

        if not phone_number:
            messagebox.showerror("Input Error", "Please enter a phone number to test")
            return

        formatted_phone = self.format_phone_number(phone_number)

        messagebox.showinfo("Adaptive Test",
                           f"Adaptive bot testing will start with basic commands\n"
                           f"and adapt based on bot responses.\n\n"
                           f"Target: {formatted_phone}\n"
                           f"This feature simulates intelligent response detection.")

        threading.Thread(target=lambda: self._run_adaptive_test_thread(formatted_phone), daemon=True).start()

    def _run_adaptive_test_thread(self, phone_number):
        """Execute adaptive bot testing"""
        self.log("🔄 ========== ADAPTIVE BOT TESTING STARTED ==========")
        self.log(f"🎯 [ADAPTIVE] Target: {phone_number}")

        # Start with basic commands and adapt based on "responses"
        conversation_state = "initial"
        messages_sent = 0
        max_messages = self.max_conversation_depth.get()

        while messages_sent < max_messages and conversation_state != "completed":
            next_message = self._get_next_adaptive_message(conversation_state, messages_sent)

            if not next_message:
                self.log(f"🏁 [ADAPTIVE] No more adaptive messages to send")
                break

            self.log(f"📤 [ADAPTIVE] State: {conversation_state} | Message: '{next_message}'")
            self.status_var.set(f"Adaptive test: {messages_sent+1}/{max_messages} - {conversation_state}")

            result, success = self.send_message(phone_number, next_message)
            messages_sent += 1

            if success:
                # Simulate response analysis and state transition
                conversation_state = self._analyze_and_transition_state(conversation_state, next_message)
                self.log(f"✅ [ADAPTIVE] Message sent | New state: {conversation_state}")
            else:
                self.log(f"❌ [ADAPTIVE] Message failed: {result}")
                break

            # Adaptive wait time based on message complexity
            wait_time = self._calculate_adaptive_wait_time(next_message)
            time.sleep(wait_time)

        self.log(f"🏁 [ADAPTIVE] Adaptive testing completed after {messages_sent} messages")
        self.status_var.set(f"Adaptive test completed: {messages_sent} messages sent")

        messagebox.showinfo("Adaptive Test Complete",
                           f"Adaptive bot test completed!\n\n"
                           f"Messages sent: {messages_sent}\n"
                           f"Final state: {conversation_state}")

    def _get_next_adaptive_message(self, state, message_count):
        """Get next message based on current conversation state"""
        state_messages = {
            "initial": ["hi", "hello"],
            "welcomed": ["menu", "help", "options"],
            "menu_shown": ["Sizing👣", "Orders & Tracking 🚚", "Chat to an agent 👥"],
            "in_category": ["Back", "Main Menu", "help"],
            "agent_flow": ["TestUser", "<EMAIL>", "12345", "I need help with my order"],
            "error_state": ["help", "menu", "reset"],
            "completed": []
        }

        available_messages = state_messages.get(state, ["help"])

        if not available_messages:
            return None

        # Choose message based on count (cycle through available options)
        return available_messages[message_count % len(available_messages)]

    def _analyze_and_transition_state(self, current_state, sent_message):
        """Analyze sent message and transition to next state"""
        # Simulate state transitions based on sent messages
        transitions = {
            ("initial", "hi"): "welcomed",
            ("initial", "hello"): "welcomed",
            ("welcomed", "menu"): "menu_shown",
            ("welcomed", "help"): "menu_shown",
            ("menu_shown", "Sizing👣"): "in_category",
            ("menu_shown", "Orders & Tracking 🚚"): "in_category",
            ("menu_shown", "Chat to an agent 👥"): "agent_flow",
            ("in_category", "Back"): "menu_shown",
            ("in_category", "Main Menu"): "menu_shown",
            ("agent_flow", "I need help with my order"): "completed",
            ("error_state", "help"): "menu_shown"
        }

        new_state = transitions.get((current_state, sent_message), current_state)

        # Add some randomness to simulate real bot behavior
        import random
        if random.random() < 0.1:  # 10% chance of unexpected state
            new_state = "error_state"

        return new_state

    def _calculate_adaptive_wait_time(self, message):
        """Calculate wait time based on message complexity"""
        # Longer wait for complex messages that might trigger longer bot responses
        complex_triggers = ["Chat to an agent", "help", "menu"]

        if any(trigger in message for trigger in complex_triggers):
            return 4.0  # Longer wait for complex responses
        else:
            return 2.0  # Standard wait time

    def run_bot_response_time_test(self):
        """Test bot response times with different message types"""
        phone_number = self.full_test_phone_var.get().strip()

        if not phone_number:
            messagebox.showerror("Input Error", "Please enter a phone number to test")
            return

        formatted_phone = self.format_phone_number(phone_number)

        if not messagebox.askyesno("Response Time Test",
                                  f"Test bot response times on {formatted_phone}?\n\n"
                                  f"This will send various message types and measure response times."):
            return

        threading.Thread(target=lambda: self._run_response_time_test_thread(formatted_phone), daemon=True).start()

    def _run_response_time_test_thread(self, phone_number):
        """Execute response time testing"""
        self.log("⏱️ ========== BOT RESPONSE TIME TEST STARTED ==========")
        self.log(f"🎯 [RESPONSE-TIME] Target: {phone_number}")

        # Different message types to test response times
        test_messages = [
            {"message": "hi", "type": "Simple greeting", "expected_time": 2.0},
            {"message": "menu", "type": "Menu request", "expected_time": 3.0},
            {"message": "help", "type": "Help request", "expected_time": 3.0},
            {"message": "Chat to an agent 👥", "type": "Complex flow trigger", "expected_time": 5.0},
            {"message": "invalid_command_xyz", "type": "Error trigger", "expected_time": 2.0}
        ]

        response_times = []

        for i, test in enumerate(test_messages):
            message = test["message"]
            msg_type = test["type"]
            expected_time = test["expected_time"]

            self.log(f"⏱️ [RESPONSE-TIME] Test {i+1}/{len(test_messages)}: {msg_type}")
            self.log(f"📤 [RESPONSE-TIME] Sending: '{message}'")
            self.status_var.set(f"Response time test: {i+1}/{len(test_messages)}")

            start_time = time.time()
            result, success = self.send_message(phone_number, message)
            send_time = time.time() - start_time

            if success:
                # Simulate waiting for and detecting bot response
                self.log(f"⏳ [RESPONSE-TIME] Waiting for bot response...")

                # Simulate response detection (in real implementation, monitor WhatsApp)
                simulated_response_time = self._simulate_response_time(message, expected_time)
                total_time = send_time + simulated_response_time

                response_times.append({
                    "type": msg_type,
                    "message": message,
                    "send_time": send_time,
                    "response_time": simulated_response_time,
                    "total_time": total_time,
                    "expected_time": expected_time,
                    "performance": "Good" if total_time <= expected_time else "Slow"
                })

                self.log(f"✅ [RESPONSE-TIME] Response detected in {total_time:.2f}s (expected: {expected_time:.1f}s)")
            else:
                self.log(f"❌ [RESPONSE-TIME] Failed to send message: {result}")

            # Wait between tests
            time.sleep(3)

        # Generate response time report
        self._generate_response_time_report(phone_number, response_times)

        self.log("🏁 [RESPONSE-TIME] ========== RESPONSE TIME TEST COMPLETED ==========")

        avg_response_time = sum(rt["total_time"] for rt in response_times) / len(response_times) if response_times else 0

        messagebox.showinfo("Response Time Test Complete",
                           f"Bot response time test completed!\n\n"
                           f"Tests completed: {len(response_times)}\n"
                           f"Average response time: {avg_response_time:.2f}s\n"
                           f"Check logs for detailed timing analysis.")

    def _simulate_response_time(self, message, expected_time):
        """Simulate bot response time based on message complexity"""
        import random

        # Add some randomness to simulate real response times
        base_time = expected_time * 0.8  # Base response time
        variation = expected_time * 0.4  # ±40% variation

        simulated_time = base_time + random.uniform(-variation, variation)
        return max(0.5, simulated_time)  # Minimum 0.5 seconds

    def _generate_response_time_report(self, phone_number, response_times):
        """Generate detailed response time analysis report"""
        self.log("📊 [RESPONSE-TIME] ========== RESPONSE TIME ANALYSIS ==========")
        self.log(f"🎯 [REPORT] Phone number: {phone_number}")
        self.log(f"📊 [REPORT] Tests completed: {len(response_times)}")

        if not response_times:
            self.log(f"❌ [REPORT] No response time data available")
            return

        # Detailed breakdown
        for rt in response_times:
            self.log(f"📊 [REPORT] {rt['type']}: {rt['total_time']:.2f}s ({rt['performance']})")
            self.log(f"📊 [REPORT]   - Message: '{rt['message']}'")
            self.log(f"📊 [REPORT]   - Send time: {rt['send_time']:.2f}s")
            self.log(f"📊 [REPORT]   - Response time: {rt['response_time']:.2f}s")
            self.log(f"📊 [REPORT]   - Expected: {rt['expected_time']:.1f}s")

        # Summary statistics
        total_times = [rt["total_time"] for rt in response_times]
        avg_time = sum(total_times) / len(total_times)
        min_time = min(total_times)
        max_time = max(total_times)
        good_performance = len([rt for rt in response_times if rt["performance"] == "Good"])

        self.log(f"")
        self.log(f"📊 [REPORT] SUMMARY STATISTICS:")
        self.log(f"📊 [REPORT] - Average response time: {avg_time:.2f}s")
        self.log(f"📊 [REPORT] - Fastest response: {min_time:.2f}s")
        self.log(f"📊 [REPORT] - Slowest response: {max_time:.2f}s")
        self.log(f"📊 [REPORT] - Good performance: {good_performance}/{len(response_times)} ({good_performance/len(response_times)*100:.1f}%)")

        # Performance assessment
        if good_performance / len(response_times) >= 0.8:
            assessment = "🟢 EXCELLENT - Bot responds quickly and consistently"
        elif good_performance / len(response_times) >= 0.6:
            assessment = "🟡 GOOD - Bot performance is acceptable with some delays"
        else:
            assessment = "🔴 POOR - Bot has significant response time issues"

        self.log(f"📊 [REPORT] Performance Assessment: {assessment}")

    def validate_phone_number(self, phone_number):
        # Support multiple phone number formats
        # 1. International format with +: +1234567890
        # 2. Numbers only: 1234567890
        # 3. Numbers with country code: 271234567890

        # Remove any spaces, dashes, or parentheses
        cleaned = re.sub(r'[\s\-\(\)]', '', phone_number)

        # Check if it's international format with +
        if re.match(r'^\+\d{7,15}$', cleaned):
            return True

        # Check if it's numbers only (7-15 digits)
        if re.match(r'^\d{7,15}$', cleaned):
            return True

        return False

    def format_phone_number(self, phone_number):
        # Clean the phone number
        cleaned = re.sub(r'[\s\-\(\)]', '', phone_number)

        # If it already has +, return as is
        if cleaned.startswith('+'):
            return cleaned

        # If it's numbers only, add + prefix
        if re.match(r'^\d{7,15}$', cleaned):
            return '+' + cleaned

        return cleaned

    def remove_recipient(self):
        selected_item = self.recipient_tree.selection()
        if not selected_item:
            messagebox.showinfo("Selection", "Please select a recipient to remove")
            return

        # Get the index of the selected item
        for item in selected_item:
            item_index = self.recipient_tree.index(item)
            # Remove from recipients list
            if 0 <= item_index < len(self.recipients):
                removed = self.recipients.pop(item_index)
                self.log(f"Removed recipient with phone number: {removed['phone_number']}")

            # Remove from treeview
            self.recipient_tree.delete(item)

    def clear_recipients(self):
        if messagebox.askyesno("Clear All", "Are you sure you want to clear all recipients?"):
            # Clear treeview
            for item in self.recipient_tree.get_children():
                self.recipient_tree.delete(item)

            # Clear recipients list
            self.recipients.clear()
            self.log("Cleared all recipients")

    def import_recipients(self):
        file_path = filedialog.askopenfilename(
            title="Import Recipients",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if not file_path:
            return

        try:
            with open(file_path, 'r') as file:
                imported_recipients = json.load(file)

            # Clear current recipients
            self.clear_recipients()

            # Add imported recipients
            for recipient in imported_recipients:
                # Support both old format (conversation_id) and new format (phone_number)
                if "phone_number" in recipient and "message" in recipient:
                    # New format
                    if self.validate_phone_number(recipient["phone_number"]):
                        self.recipients.append(recipient)
                        self.recipient_tree.insert("", tk.END, values=(recipient["phone_number"], recipient["message"]))
                    else:
                        self.log(f"Skipped invalid phone number: {recipient['phone_number']}")
                elif "conversation_id" in recipient and "message" in recipient:
                    # Convert old format to new format if it looks like a phone number
                    if self.validate_phone_number(recipient["conversation_id"]):
                        phone_number = recipient["conversation_id"]
                        new_recipient = {"phone_number": phone_number, "message": recipient["message"]}
                        self.recipients.append(new_recipient)
                        self.recipient_tree.insert("", tk.END, values=(phone_number, recipient["message"]))
                        self.log(f"Converted conversation ID to phone number: {phone_number}")
                    else:
                        # Not a valid phone number, create a placeholder
                        self.log(f"Warning: Imported conversation ID is not a valid phone number: {recipient['conversation_id']}")

            self.log(f"Imported {len(self.recipients)} recipients from {file_path}")
            messagebox.showinfo("Import", f"Successfully imported {len(self.recipients)} recipients")

        except Exception as e:
            self.log(f"Error importing recipients: {str(e)}")
            messagebox.showerror("Import Error", f"Failed to import recipients: {str(e)}")

    def export_recipients(self):
        if not self.recipients:
            messagebox.showinfo("Export", "No recipients to export")
            return

        file_path = filedialog.asksaveasfilename(
            title="Export Recipients",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if not file_path:
            return

        try:
            with open(file_path, 'w') as file:
                json.dump(self.recipients, file, indent=4)

            self.log(f"Exported {len(self.recipients)} recipients to {file_path}")
            messagebox.showinfo("Export", f"Successfully exported {len(self.recipients)} recipients")

        except Exception as e:
            self.log(f"Error exporting recipients: {str(e)}")
            messagebox.showerror("Export Error", f"Failed to export recipients: {str(e)}")

    def add_channel(self):
        name = self.channel_name_var.get().strip()
        channel_type = self.channel_type_var.get()

        if not name:
            messagebox.showerror("Input Error", "Please enter a channel name")
            return

        # Check for duplicate names
        for channel in self.channels:
            if channel["name"] == name:
                messagebox.showerror("Input Error", f"Channel with name '{name}' already exists")
                return

        # Create channel object based on type
        if channel_type == "whatsapp":
            # WhatsApp Business API (Cloud API) - requires Phone Number ID and Access Token
            phone_id = self.channel_phone_id_var.get().strip()
            token = self.channel_token_var.get().strip()

            if not phone_id or not token:
                messagebox.showerror("Input Error", "Please enter both Phone Number ID and Access Token for WhatsApp Business API")
                return

            channel = {
                "name": name,
                "type": "whatsapp",
                "phone_number_id": phone_id,
                "access_token": token,
                "status": "Active"
            }
        else:  # sunco
            # Sunco API - only requires App ID and API Key (no Phone Number ID)
            app_id = self.channel_app_id_var.get().strip()
            api_key = self.channel_api_key_var.get().strip()

            if not app_id or not api_key:
                messagebox.showerror("Input Error", "Please enter both App ID and API Key for Sunco API")
                return

            channel = {
                "name": name,
                "type": "sunco",
                "app_id": app_id,
                "api_key": api_key,
                "status": "Active"
            }

        # Add to channels list
        self.channels.append(channel)

        # Add to treeview
        self.channel_tree.insert("", tk.END, values=(name, channel_type, "Active"))

        # Clear input fields
        self.channel_name_var.set("")
        self.channel_phone_id_var.set("")
        self.channel_token_var.set("")
        self.channel_app_id_var.set("")
        self.channel_api_key_var.set("")

        # Set as active if it's the first channel
        if len(self.channels) == 1:
            self.active_channel.set(name)
            self.log(f"Channel '{name}' added and set as active")
        else:
            self.log(f"Channel '{name}' added")

        # Save channels to config
        self.save_config()

    def remove_channel(self):
        selected_item = self.channel_tree.selection()
        if not selected_item:
            messagebox.showinfo("Selection", "Please select a channel to remove")
            return

        # Get the selected channel name
        item = selected_item[0]
        channel_name = self.channel_tree.item(item, "values")[0]

        # Confirm removal
        if not messagebox.askyesno("Remove Channel", f"Are you sure you want to remove channel '{channel_name}'?"):
            return

        # Remove from channels list
        for i, channel in enumerate(self.channels):
            if channel["name"] == channel_name:
                removed = self.channels.pop(i)
                break

        # Remove from treeview
        self.channel_tree.delete(item)

        # Update active channel if needed
        if self.active_channel.get() == channel_name:
            if self.channels:
                self.active_channel.set(self.channels[0]["name"])
                self.log(f"Active channel changed to '{self.channels[0]['name']}'")
            else:
                self.active_channel.set("")

        self.log(f"Channel '{channel_name}' removed")

        # Save channels to config
        self.save_config()

    def test_channel_connection(self):
        selected_item = self.channel_tree.selection()
        if not selected_item:
            messagebox.showinfo("Selection", "Please select a channel to test")
            return

        # Get the selected channel name
        item = selected_item[0]
        channel_name = self.channel_tree.item(item, "values")[0]

        # Find the channel in the list
        channel = None
        for ch in self.channels:
            if ch["name"] == channel_name:
                channel = ch
                break

        if not channel:
            messagebox.showerror("Error", "Channel not found")
            return

        # Test connection based on channel type
        if channel["type"] == "whatsapp":
            result = self.test_whatsapp_connection(channel["phone_number_id"], channel["access_token"])
        else:  # sunco
            result = self.test_sunco_connection(channel["app_id"], channel["api_key"])

        if result:
            messagebox.showinfo("Connection Test", f"Successfully connected to {channel['type']} API")
            # Update status in treeview
            self.channel_tree.item(item, values=(channel_name, channel["type"], "Active"))
            # Update status in channel object
            channel["status"] = "Active"
        else:
            messagebox.showerror("Connection Test", f"Failed to connect to {channel['type']} API")
            # Update status in treeview
            self.channel_tree.item(item, values=(channel_name, channel["type"], "Error"))
            # Update status in channel object
            channel["status"] = "Error"

        # Save channels to config
        self.save_config()

    def test_whatsapp_connection(self, phone_number_id, access_token):
        try:
            self.log(f"🔍 [TEST] Testing WhatsApp Business API connection...")
            self.log(f"🔍 [TEST] Phone Number ID: {phone_number_id}")
            self.log(f"🔍 [TEST] Access Token: {access_token[:10]}...{access_token[-4:] if len(access_token) > 14 else access_token}")

            # Simple API call to test connection
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }

            # Use the /business_profile endpoint which is a lightweight call
            url = f"https://graph.facebook.com/v17.0/{phone_number_id}/whatsapp_business_profile"
            self.log(f"🌐 [TEST] Testing URL: {url}")

            response = requests.get(url, headers=headers)
            self.log(f"📥 [TEST] Response status: {response.status_code}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    self.log(f"✅ [TEST] WhatsApp connection successful!")
                    self.log(f"✅ [TEST] Business profile data: {json.dumps(data, indent=2)}")
                except:
                    self.log(f"✅ [TEST] WhatsApp connection successful (could not parse response)")
                return True
            else:
                try:
                    error_data = response.json()
                    self.log(f"❌ [TEST] WhatsApp connection failed!")
                    self.log(f"❌ [TEST] Error response: {json.dumps(error_data, indent=2)}")
                except:
                    self.log(f"❌ [TEST] WhatsApp connection failed! Raw response: {response.text}")
                return False

        except Exception as e:
            self.log(f"💥 [TEST] WhatsApp connection test exception: {str(e)}")
            import traceback
            self.log(f"💥 [TEST] Traceback: {traceback.format_exc()}")
            return False

    def test_sunco_connection(self, app_id, api_key):
        try:
            self.log(f"🔍 [TEST] Testing Sunco API connection...")
            self.log(f"🔍 [TEST] App ID: {app_id}")
            self.log(f"🔍 [TEST] API Key: {api_key[:8]}...{api_key[-4:] if len(api_key) > 12 else api_key}")

            # Simple API call to test connection
            auth_string = f"{app_id}:{api_key}"
            encoded_auth = base64.b64encode(auth_string.encode()).decode("utf-8")

            headers = {
                "Authorization": f"Basic {encoded_auth}",
                "Content-Type": "application/json"
            }

            # Use the /apps endpoint which is a lightweight call
            url = f"https://api.smooch.io/v1.1/apps/{app_id}"
            self.log(f"🌐 [TEST] Testing URL: {url}")

            response = requests.get(url, headers=headers)
            self.log(f"📥 [TEST] Response status: {response.status_code}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    self.log(f"✅ [TEST] Sunco connection successful!")
                    self.log(f"✅ [TEST] App data: {json.dumps(data, indent=2)}")
                except:
                    self.log(f"✅ [TEST] Sunco connection successful (could not parse response)")
                return True
            else:
                try:
                    error_data = response.json()
                    self.log(f"❌ [TEST] Sunco connection failed!")
                    self.log(f"❌ [TEST] Error response: {json.dumps(error_data, indent=2)}")
                except:
                    self.log(f"❌ [TEST] Sunco connection failed! Raw response: {response.text}")
                return False

        except Exception as e:
            self.log(f"💥 [TEST] Sunco connection test exception: {str(e)}")
            import traceback
            self.log(f"💥 [TEST] Traceback: {traceback.format_exc()}")
            return False

    def set_active_channel(self):
        selected_item = self.channel_tree.selection()
        if not selected_item:
            messagebox.showinfo("Selection", "Please select a channel to set as active")
            return

        # Get the selected channel name
        item = selected_item[0]
        channel_name = self.channel_tree.item(item, "values")[0]

        # Set as active
        self.active_channel.set(channel_name)
        self.log(f"Channel '{channel_name}' set as active")

        # Save to config
        self.save_config()

    def save_settings(self):
        # Update API usage limit
        self.api_usage["daily_limit"] = self.daily_limit_var.get()

        # Update usage display
        self.usage_count_var.set(f"{self.api_usage['daily_count']} / {self.api_usage['daily_limit']}")

        # Save to config
        self.save_config()

        self.log("Settings saved")
        messagebox.showinfo("Settings", "Settings saved successfully")

    def export_message_history(self):
        if not self.message_history:
            messagebox.showinfo("Export", "No message history to export")
            return

        file_path = filedialog.asksaveasfilename(
            title="Export Message History",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if not file_path:
            return

        try:
            with open(file_path, 'w', newline='') as file:
                import csv
                writer = csv.writer(file)
                # Write header
                writer.writerow(["Timestamp", "Recipient", "Status", "Message", "Channel", "Test Mode"])

                # Write data
                for msg in self.message_history:
                    writer.writerow([
                        msg.get("timestamp", ""),
                        msg.get("recipient", ""),
                        msg.get("status", ""),
                        msg.get("message", ""),
                        msg.get("channel", ""),
                        "Yes" if msg.get("test_mode", False) else "No"
                    ])

            self.log(f"Exported {len(self.message_history)} message history records to {file_path}")
            messagebox.showinfo("Export", f"Successfully exported message history")

        except Exception as e:
            self.log(f"Error exporting message history: {str(e)}")
            messagebox.showerror("Export Error", f"Failed to export message history: {str(e)}")

    def save_config(self):
        config = {
            "api_type": self.api_type.get(),
            "app_id": self.app_id.get(),
            "api_key": self.api_key.get(),
            "phone_number_id": self.phone_number_id.get(),
            "access_token": self.access_token.get(),
            "test_mode": self.test_mode.get(),
            "delay_between_messages": self.delay_between_messages.get(),
            "max_messages_per_minute": self.max_messages_per_minute.get(),
            "api_usage": self.api_usage,
            "channels": self.channels,
            "active_channel": self.active_channel.get()
        }

        try:
            with open("config.json", 'w') as file:
                json.dump(config, file, indent=4)

            self.log("Configuration saved")

        except Exception as e:
            self.log(f"Error saving configuration: {str(e)}")
            messagebox.showerror("Save Error", f"Failed to save configuration: {str(e)}")

    def load_config(self):
        if not os.path.exists("config.json"):
            return

        try:
            with open("config.json", 'r') as file:
                config = json.load(file)

            # Set API type first so the correct frame is displayed
            api_type = config.get("api_type", "sunco")
            self.api_type.set(api_type)

            # Load Sunco API credentials
            self.app_id.set(config.get("app_id", ""))
            self.api_key.set(config.get("api_key", ""))

            # Load WhatsApp Business API credentials
            self.phone_number_id.set(config.get("phone_number_id", ""))
            self.access_token.set(config.get("access_token", ""))

            # Load settings
            self.test_mode.set(config.get("test_mode", True))
            self.delay_between_messages.set(config.get("delay_between_messages", 1.0))
            self.max_messages_per_minute.set(config.get("max_messages_per_minute", 30))

            # Load API usage
            if "api_usage" in config:
                self.api_usage = config["api_usage"]
                self.daily_limit_var.set(self.api_usage["daily_limit"])
                self.usage_count_var.set(f"{self.api_usage['daily_count']} / {self.api_usage['daily_limit']}")
                self.last_reset_var.set(self.api_usage["last_reset"])

            # Load channels
            if "channels" in config:
                self.channels = config["channels"]
                # Populate channel treeview
                for channel in self.channels:
                    self.channel_tree.insert("", tk.END, values=(
                        channel["name"],
                        channel["type"],
                        channel.get("status", "Active")
                    ))

            # Set active channel
            if "active_channel" in config and config["active_channel"]:
                self.active_channel.set(config["active_channel"])

            # Update UI based on loaded API type
            self.toggle_api_fields()

            self.log("Configuration loaded")

        except Exception as e:
            self.log(f"Error loading configuration: {str(e)}")

    def send_message_sunco(self, conversation_id, message, channel=None):
        try:
            self.log(f"🔄 [SUNCO] Starting message send to conversation ID: {conversation_id}")
            self.log(f"📝 [SUNCO] Message content: '{message[:50]}{'...' if len(message) > 50 else ''}'")

            # Check if we're in test mode
            if self.test_mode.get():
                self.log(f"🧪 [TEST MODE] Simulating Sunco API call")
                self.log(f"🧪 [TEST MODE] Would send to conversation ID: {conversation_id}")
                self.log(f"🧪 [TEST MODE] Message would be: {message}")
                return f"[TEST MODE] Message would be sent to conversation ID {conversation_id}", True

            # Get credentials from channel if provided, otherwise use global credentials
            if channel:
                app_id = channel["app_id"]
                api_key = channel["api_key"]
                self.log(f"🔑 [SUNCO] Using channel credentials: {channel['name']}")
                self.log(f"🔑 [SUNCO] App ID: {app_id[:8]}...{app_id[-4:] if len(app_id) > 12 else app_id}")
            else:
                app_id = self.app_id.get()
                api_key = self.api_key.get()
                self.log(f"🔑 [SUNCO] Using global credentials")
                self.log(f"🔑 [SUNCO] App ID: {app_id[:8]}...{app_id[-4:] if len(app_id) > 12 else app_id}")

            if not app_id or not api_key:
                self.log(f"❌ [SUNCO] Missing credentials - App ID: {'✓' if app_id else '✗'}, API Key: {'✓' if api_key else '✗'}")
                return "Sunco API credentials not configured", False

            # Check API usage limits
            if self.api_usage["daily_count"] >= self.api_usage["daily_limit"]:
                self.log(f"🚫 [SUNCO] Daily limit reached: {self.api_usage['daily_count']}/{self.api_usage['daily_limit']}")
                return f"Daily API usage limit reached ({self.api_usage['daily_limit']} messages)", False

            self.log(f"📊 [SUNCO] API usage: {self.api_usage['daily_count']}/{self.api_usage['daily_limit']} messages today")

            # Create the Basic Authentication header using base64 encoding
            auth_string = f"{app_id}:{api_key}"
            encoded_auth = base64.b64encode(auth_string.encode()).decode("utf-8")

            headers = {
                "Authorization": f"Basic {encoded_auth}",
                "Content-Type": "application/json"
            }
            data = {
                "role": "appUser",  # Sends the message as the user; "appMaker" sends it as the app
                "type": "text",
                "text": message
            }
            url = self.sunco_api_url.replace("{app_id}", app_id).replace("{conversation_id}", conversation_id)

            self.log(f"🌐 [SUNCO] API URL: {url}")
            self.log(f"📤 [SUNCO] Request payload: {json.dumps(data, indent=2)}")
            self.log(f"🔐 [SUNCO] Auth header: Basic {encoded_auth[:10]}...{encoded_auth[-4:]}")

            self.log(f"⏳ [SUNCO] Sending HTTP POST request...")
            response = requests.post(url, headers=headers, json=data)

            self.log(f"📥 [SUNCO] Response received - Status: {response.status_code}")
            self.log(f"📥 [SUNCO] Response headers: {dict(response.headers)}")

            # Increment API usage counter
            self.api_usage["daily_count"] += 1
            self.usage_count_var.set(f"{self.api_usage['daily_count']} / {self.api_usage['daily_limit']}")
            self.log(f"📊 [SUNCO] Updated usage count: {self.api_usage['daily_count']}/{self.api_usage['daily_limit']}")

            if response.status_code == 201:
                response_data = response.json()
                self.log(f"✅ [SUNCO] SUCCESS - Message sent successfully!")
                self.log(f"✅ [SUNCO] Response data: {json.dumps(response_data, indent=2)}")
                if 'message' in response_data and '_id' in response_data['message']:
                    self.log(f"✅ [SUNCO] Message ID: {response_data['message']['_id']}")
                return f"Message sent to conversation ID {conversation_id}", True
            else:
                try:
                    error_data = response.json()
                    self.log(f"❌ [SUNCO] FAILED - Status {response.status_code}")
                    self.log(f"❌ [SUNCO] Error response: {json.dumps(error_data, indent=2)}")
                except:
                    self.log(f"❌ [SUNCO] FAILED - Status {response.status_code}")
                    self.log(f"❌ [SUNCO] Raw response: {response.text}")
                return f"Failed to send message to {conversation_id}: Status {response.status_code}", False

        except Exception as e:
            self.log(f"💥 [SUNCO] EXCEPTION occurred: {str(e)}")
            self.log(f"💥 [SUNCO] Exception type: {type(e).__name__}")
            import traceback
            self.log(f"💥 [SUNCO] Traceback: {traceback.format_exc()}")
            return f"Error occurred with Sunco API: {str(e)}", False

    def send_message_whatsapp(self, phone_number, message, channel=None):
        try:
            self.log(f"🔄 [WHATSAPP] Starting message send to phone: {phone_number}")
            self.log(f"📝 [WHATSAPP] Message content: '{message[:50]}{'...' if len(message) > 50 else ''}'")

            # Check if we're in test mode
            if self.test_mode.get():
                self.log(f"🧪 [TEST MODE] Simulating WhatsApp Business API call")
                self.log(f"🧪 [TEST MODE] Would send to phone: {phone_number}")
                self.log(f"🧪 [TEST MODE] Message would be: {message}")
                return f"[TEST MODE] Message would be sent to phone number {phone_number}", True

            # Get credentials from channel if provided, otherwise use global credentials
            if channel:
                phone_number_id = channel["phone_number_id"]
                access_token = channel["access_token"]
                self.log(f"🔑 [WHATSAPP] Using channel credentials: {channel['name']}")
                self.log(f"🔑 [WHATSAPP] Phone Number ID: {phone_number_id}")
            else:
                phone_number_id = self.phone_number_id.get()
                access_token = self.access_token.get()
                self.log(f"🔑 [WHATSAPP] Using global credentials")
                self.log(f"🔑 [WHATSAPP] Phone Number ID: {phone_number_id}")

            if not phone_number_id or not access_token:
                self.log(f"❌ [WHATSAPP] Missing credentials - Phone Number ID: {'✓' if phone_number_id else '✗'}, Access Token: {'✓' if access_token else '✗'}")
                return "WhatsApp Business API credentials not configured", False

            # Check API usage limits
            if self.api_usage["daily_count"] >= self.api_usage["daily_limit"]:
                self.log(f"🚫 [WHATSAPP] Daily limit reached: {self.api_usage['daily_count']}/{self.api_usage['daily_limit']}")
                return f"Daily API usage limit reached ({self.api_usage['daily_limit']} messages)", False

            self.log(f"📊 [WHATSAPP] API usage: {self.api_usage['daily_count']}/{self.api_usage['daily_limit']} messages today")

            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }

            # Format for WhatsApp Business API
            data = {
                "messaging_product": "whatsapp",
                "recipient_type": "individual",
                "to": phone_number,
                "type": "text",
                "text": {
                    "body": message
                }
            }

            url = self.whatsapp_api_url.replace("{phone_number_id}", phone_number_id)

            self.log(f"🌐 [WHATSAPP] API URL: {url}")
            self.log(f"📤 [WHATSAPP] Request payload: {json.dumps(data, indent=2)}")
            self.log(f"🔐 [WHATSAPP] Auth header: Bearer {access_token[:10]}...{access_token[-4:] if len(access_token) > 14 else access_token}")

            self.log(f"⏳ [WHATSAPP] Sending HTTP POST request...")
            response = requests.post(url, headers=headers, json=data)

            self.log(f"📥 [WHATSAPP] Response received - Status: {response.status_code}")
            self.log(f"📥 [WHATSAPP] Response headers: {dict(response.headers)}")

            # Increment API usage counter
            self.api_usage["daily_count"] += 1
            self.usage_count_var.set(f"{self.api_usage['daily_count']} / {self.api_usage['daily_limit']}")
            self.log(f"📊 [WHATSAPP] Updated usage count: {self.api_usage['daily_count']}/{self.api_usage['daily_limit']}")

            if response.status_code == 200:
                try:
                    response_data = response.json()
                    self.log(f"✅ [WHATSAPP] SUCCESS - Message sent successfully!")
                    self.log(f"✅ [WHATSAPP] Response data: {json.dumps(response_data, indent=2)}")
                    if 'messages' in response_data and len(response_data['messages']) > 0:
                        message_id = response_data['messages'][0].get('id', 'Unknown')
                        self.log(f"✅ [WHATSAPP] Message ID: {message_id}")
                except:
                    self.log(f"✅ [WHATSAPP] SUCCESS - Message sent (could not parse response JSON)")
                    self.log(f"✅ [WHATSAPP] Raw response: {response.text}")
                return f"Message sent to phone number {phone_number}", True
            else:
                try:
                    error_data = response.json()
                    self.log(f"❌ [WHATSAPP] FAILED - Status {response.status_code}")
                    self.log(f"❌ [WHATSAPP] Error response: {json.dumps(error_data, indent=2)}")
                    if 'error' in error_data:
                        error_info = error_data['error']
                        self.log(f"❌ [WHATSAPP] Error code: {error_info.get('code', 'Unknown')}")
                        self.log(f"❌ [WHATSAPP] Error message: {error_info.get('message', 'Unknown')}")
                except:
                    self.log(f"❌ [WHATSAPP] FAILED - Status {response.status_code}")
                    self.log(f"❌ [WHATSAPP] Raw response: {response.text}")
                return f"Failed to send message to {phone_number}: Status {response.status_code}", False

        except Exception as e:
            self.log(f"💥 [WHATSAPP] EXCEPTION occurred: {str(e)}")
            self.log(f"💥 [WHATSAPP] Exception type: {type(e).__name__}")
            import traceback
            self.log(f"💥 [WHATSAPP] Traceback: {traceback.format_exc()}")
            return f"Error occurred with WhatsApp Business API: {str(e)}", False

    def send_message(self, recipient, message):
        self.log(f"🚀 [MAIN] ===== Starting message send process =====")
        self.log(f"🎯 [MAIN] Target recipient: {recipient}")
        self.log(f"📝 [MAIN] Message: '{message[:100]}{'...' if len(message) > 100 else ''}'")

        # Get active channel if available
        active_channel_name = self.active_channel.get()
        channel = None

        if active_channel_name:
            self.log(f"📡 [MAIN] Looking for active channel: {active_channel_name}")
            # Find the active channel in the list
            for ch in self.channels:
                if ch["name"] == active_channel_name:
                    channel = ch
                    self.log(f"✅ [MAIN] Found active channel: {ch['name']} (Type: {ch['type']})")
                    break

            if not channel:
                self.log(f"⚠️ [MAIN] Active channel '{active_channel_name}' not found in channel list")
        else:
            self.log(f"📡 [MAIN] No active channel set, using global credentials")

        # Record in message history
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        history_entry = {
            "timestamp": timestamp,
            "recipient": recipient,
            "message": message,
            "channel": active_channel_name if active_channel_name else "Default",
            "test_mode": self.test_mode.get()
        }

        # Determine which API to use based on channel or global setting
        api_type = channel["type"] if channel else self.api_type.get()
        self.log(f"🔧 [MAIN] API type determined: {api_type}")

        if api_type == "whatsapp":
            self.log(f"📱 [MAIN] Routing to WhatsApp Business API handler")
            # For WhatsApp Business API, use phone number
            result, success = self.send_message_whatsapp(recipient, message, channel)
        else:
            self.log(f"💬 [MAIN] Routing to Sunco API handler")
            # For Sunco API, use conversation ID
            result, success = self.send_message_sunco(recipient, message, channel)

        # Update history entry with result
        history_entry["status"] = "Success" if success else "Failed"
        self.message_history.append(history_entry)

        # Add to history treeview
        self.history_tree.insert("", 0, values=(
            timestamp,
            recipient,
            "Success" if success else "Failed",
            message[:50] + "..." if len(message) > 50 else message
        ))

        if success:
            self.log(f"🎉 [MAIN] ===== Message send completed successfully =====")
        else:
            self.log(f"💔 [MAIN] ===== Message send failed =====")

        self.log(f"📊 [MAIN] Final result: {result}")

        return result, success

    def send_messages(self):
        if not self.recipients:
            messagebox.showinfo("Send", "No recipients to send messages to")
            return

        # Validate API configuration
        if not self.app_id.get() or not self.api_key.get():
            messagebox.showerror("API Configuration", "Please configure your API credentials first")
            return

        # Confirm sending
        if not messagebox.askyesno("Send Messages", f"Send messages to {len(self.recipients)} recipients?"):
            return

        # Start sending in a separate thread to avoid freezing the UI
        threading.Thread(target=self._send_messages_thread, daemon=True).start()

    def _send_messages_thread(self):
        self.log("🚀 ========== BULK MESSAGE SENDING STARTED ==========")
        self.status_var.set("Initializing bulk send...")

        # Get active channel if available
        active_channel_name = self.active_channel.get()
        if active_channel_name:
            self.log(f"📡 [BATCH] Active channel: {active_channel_name}")
        else:
            self.log("📡 [BATCH] No active channel - using default credentials")

        # Test mode notification
        if self.test_mode.get():
            self.log("🧪 [BATCH] TEST MODE ENABLED - Messages will be logged but not actually sent")
        else:
            self.log("🔴 [BATCH] LIVE MODE - Messages will be sent to real recipients")

        self.log(f"📊 [BATCH] Total recipients to process: {len(self.recipients)}")

        success_count = 0
        fail_count = 0

        # Calculate rate limiting
        delay = self.delay_between_messages.get()
        max_per_minute = self.max_messages_per_minute.get()

        # If max_per_minute is set, calculate the required delay
        if max_per_minute > 0:
            calculated_delay = 60.0 / max_per_minute
            # Use the larger of the two delays
            delay = max(delay, calculated_delay)
            self.log(f"⏱️ [BATCH] Rate limiting: {max_per_minute} messages per minute (delay: {delay:.2f} seconds)")
        else:
            self.log(f"⏱️ [BATCH] Rate limiting: {delay:.2f} seconds between messages")

        # Estimate total time
        total_time = (len(self.recipients) - 1) * delay
        self.log(f"⏰ [BATCH] Estimated completion time: {total_time:.1f} seconds ({total_time/60:.1f} minutes)")

        start_time = time.time()

        # Process each recipient
        for i, recipient in enumerate(self.recipients):
            message = recipient["message"]

            self.log(f"📋 [BATCH] ===== Processing recipient {i+1}/{len(self.recipients)} =====")

            # Get the appropriate identifier based on active channel or global API type
            api_type = None
            if active_channel_name:
                # Find the active channel
                for ch in self.channels:
                    if ch["name"] == active_channel_name:
                        api_type = ch["type"]
                        break

            if not api_type:
                api_type = self.api_type.get()

            self.log(f"🔧 [BATCH] API type for this message: {api_type}")

            if api_type == "whatsapp":
                # For WhatsApp Business API, use phone number
                if "phone_number" in recipient:
                    identifier = recipient["phone_number"]
                    self.log(f"📱 [BATCH] Using phone number: {identifier}")
                else:
                    self.log(f"❌ [BATCH] Skipping recipient {i+1}: No phone number found")
                    fail_count += 1
                    continue
            else:
                # For Sunco API, use conversation ID if available, otherwise try phone number
                if "conversation_id" in recipient:
                    identifier = recipient["conversation_id"]
                    self.log(f"💬 [BATCH] Using conversation ID: {identifier}")
                elif "phone_number" in recipient:
                    identifier = recipient["phone_number"]
                    self.log(f"💬 [BATCH] Using phone number as conversation ID: {identifier}")
                else:
                    self.log(f"❌ [BATCH] Skipping recipient {i+1}: No identifier found")
                    fail_count += 1
                    continue

            self.status_var.set(f"Sending message {i+1}/{len(self.recipients)}...")

            # Check if we've hit the daily limit
            if self.api_usage["daily_count"] >= self.api_usage["daily_limit"] and not self.test_mode.get():
                self.log(f"🚫 [BATCH] Daily API usage limit reached ({self.api_usage['daily_limit']} messages)")
                messagebox.showwarning("API Limit", f"Daily API usage limit reached ({self.api_usage['daily_limit']} messages)")
                break

            # Send the message
            result, success = self.send_message(identifier, message)

            if success:
                success_count += 1
                self.log(f"✅ [BATCH] Recipient {i+1} completed successfully")
            else:
                fail_count += 1
                self.log(f"❌ [BATCH] Recipient {i+1} failed")

                # If we hit an API limit, stop sending
                if "limit reached" in result.lower() and not self.test_mode.get():
                    self.log("🚫 [BATCH] Stopping due to API limit")
                    break

            # Progress update
            progress_percent = ((i + 1) / len(self.recipients)) * 100
            elapsed_time = time.time() - start_time
            if i > 0:
                avg_time_per_message = elapsed_time / (i + 1)
                remaining_messages = len(self.recipients) - (i + 1)
                eta = remaining_messages * avg_time_per_message
                self.log(f"📈 [BATCH] Progress: {progress_percent:.1f}% | ETA: {eta:.1f} seconds")

            # Rate limiting
            if i < len(self.recipients) - 1:  # Don't sleep after the last message
                self.log(f"⏳ [BATCH] Waiting {delay:.2f} seconds before next message...")
                time.sleep(delay)

        # Final statistics
        total_elapsed = time.time() - start_time
        self.log(f"🏁 [BATCH] ========== BULK SENDING COMPLETED ==========")
        self.log(f"📊 [BATCH] Final Statistics:")
        self.log(f"📊 [BATCH] - Total processed: {success_count + fail_count}")
        self.log(f"📊 [BATCH] - Successful: {success_count}")
        self.log(f"📊 [BATCH] - Failed: {fail_count}")
        self.log(f"📊 [BATCH] - Success rate: {(success_count/(success_count+fail_count)*100):.1f}%" if (success_count + fail_count) > 0 else "📊 [BATCH] - Success rate: N/A")
        self.log(f"📊 [BATCH] - Total time: {total_elapsed:.1f} seconds ({total_elapsed/60:.1f} minutes)")
        self.log(f"📊 [BATCH] - Average time per message: {total_elapsed/(success_count+fail_count):.2f} seconds" if (success_count + fail_count) > 0 else "📊 [BATCH] - Average time: N/A")

        # Save config to persist API usage
        self.save_config()

        self.status_var.set(f"Completed: {success_count} sent, {fail_count} failed")

        messagebox.showinfo("Send Complete", f"Message sending completed:\n{success_count} sent successfully\n{fail_count} failed\n\nTotal time: {total_elapsed:.1f} seconds")

    def test_all_channels(self):
        """Test all configured channels with the current recipient list"""
        if not self.recipients:
            messagebox.showinfo("Test All Channels", "No recipients to send messages to")
            return

        if not self.channels:
            messagebox.showinfo("Test All Channels", "No channels configured")
            return

        # Confirm testing
        if not messagebox.askyesno("Test All Channels",
                                  f"Test {len(self.channels)} channels with {len(self.recipients)} recipients each?\n\n"
                                  f"Total messages: {len(self.channels) * len(self.recipients)}"):
            return

        # Start testing in a separate thread
        threading.Thread(target=self._test_all_channels_thread, daemon=True).start()

    def _test_all_channels_thread(self):
        """Test all channels sequentially"""
        self.log("🚀 ========== MULTI-CHANNEL BOT TESTING STARTED ==========")

        original_active_channel = self.active_channel.get()
        total_channels = len(self.channels)
        overall_results = {}

        for i, channel in enumerate(self.channels):
            channel_name = channel["name"]
            channel_type = channel["type"]

            self.log(f"🤖 [MULTI-BOT] ===== Testing Bot {i+1}/{total_channels}: {channel_name} ({channel_type}) =====")

            # Set this channel as active
            self.active_channel.set(channel_name)
            self.status_var.set(f"Testing channel {i+1}/{total_channels}: {channel_name}")

            # Track results for this channel
            channel_results = {
                "name": channel_name,
                "type": channel_type,
                "success": 0,
                "failed": 0,
                "messages": []
            }

            # Send all recipients to this channel
            for j, recipient in enumerate(self.recipients):
                message = recipient["message"]

                # Get identifier based on channel type
                if channel_type == "whatsapp":
                    if "phone_number" in recipient:
                        identifier = recipient["phone_number"]
                    else:
                        self.log(f"❌ [MULTI-BOT] Skipping recipient {j+1} for {channel_name}: No phone number")
                        channel_results["failed"] += 1
                        continue
                else:  # sunco
                    if "conversation_id" in recipient:
                        identifier = recipient["conversation_id"]
                    elif "phone_number" in recipient:
                        identifier = recipient["phone_number"]
                    else:
                        self.log(f"❌ [MULTI-BOT] Skipping recipient {j+1} for {channel_name}: No identifier")
                        channel_results["failed"] += 1
                        continue

                self.log(f"🎯 [MULTI-BOT] {channel_name} -> Recipient {j+1}/{len(self.recipients)}: {identifier}")

                # Send message
                result, success = self.send_message(identifier, message)

                if success:
                    channel_results["success"] += 1
                    self.log(f"✅ [MULTI-BOT] {channel_name} -> Recipient {j+1} SUCCESS")
                else:
                    channel_results["failed"] += 1
                    self.log(f"❌ [MULTI-BOT] {channel_name} -> Recipient {j+1} FAILED: {result}")

                # Store message result
                channel_results["messages"].append({
                    "recipient": identifier,
                    "message": message[:50] + "..." if len(message) > 50 else message,
                    "success": success,
                    "result": result
                })

                # Small delay between messages to same channel
                if j < len(self.recipients) - 1:
                    time.sleep(self.delay_between_messages.get())

            # Store channel results
            overall_results[channel_name] = channel_results

            # Log channel summary
            total_for_channel = channel_results["success"] + channel_results["failed"]
            success_rate = (channel_results["success"] / total_for_channel * 100) if total_for_channel > 0 else 0
            self.log(f"📊 [MULTI-BOT] {channel_name} Summary: {channel_results['success']}/{total_for_channel} successful ({success_rate:.1f}%)")

            # Delay between channels
            if i < len(self.channels) - 1:
                self.log(f"⏳ [MULTI-BOT] Waiting before testing next channel...")
                time.sleep(2)  # 2 second delay between channels

        # Restore original active channel
        self.active_channel.set(original_active_channel)

        # Generate comprehensive report
        self._generate_multi_bot_report(overall_results)

        self.log("🏁 [MULTI-BOT] ========== MULTI-CHANNEL BOT TESTING COMPLETED ==========")
        self.status_var.set("Multi-channel testing completed")

    def _generate_multi_bot_report(self, results):
        """Generate a comprehensive report of multi-bot testing"""
        self.log("📋 [MULTI-BOT] ========== COMPREHENSIVE TEST REPORT ==========")

        total_channels_tested = len(results)
        total_messages_sent = 0
        total_successful = 0
        total_failed = 0

        # Channel-by-channel breakdown
        for channel_name, channel_data in results.items():
            channel_total = channel_data["success"] + channel_data["failed"]
            channel_success_rate = (channel_data["success"] / channel_total * 100) if channel_total > 0 else 0

            self.log(f"🤖 [REPORT] Bot: {channel_name} ({channel_data['type']})")
            self.log(f"📊 [REPORT] - Messages sent: {channel_total}")
            self.log(f"📊 [REPORT] - Successful: {channel_data['success']}")
            self.log(f"📊 [REPORT] - Failed: {channel_data['failed']}")
            self.log(f"📊 [REPORT] - Success rate: {channel_success_rate:.1f}%")

            # Show failed messages for debugging
            failed_messages = [msg for msg in channel_data["messages"] if not msg["success"]]
            if failed_messages:
                self.log(f"❌ [REPORT] Failed messages for {channel_name}:")
                for msg in failed_messages[:3]:  # Show first 3 failures
                    self.log(f"❌ [REPORT]   - {msg['recipient']}: {msg['result']}")
                if len(failed_messages) > 3:
                    self.log(f"❌ [REPORT]   - ... and {len(failed_messages) - 3} more failures")

            total_messages_sent += channel_total
            total_successful += channel_data["success"]
            total_failed += channel_data["failed"]
            self.log(f"")

        # Overall summary
        overall_success_rate = (total_successful / total_messages_sent * 100) if total_messages_sent > 0 else 0

        self.log(f"🎯 [REPORT] OVERALL SUMMARY:")
        self.log(f"📊 [REPORT] - Channels tested: {total_channels_tested}")
        self.log(f"📊 [REPORT] - Total messages sent: {total_messages_sent}")
        self.log(f"📊 [REPORT] - Total successful: {total_successful}")
        self.log(f"📊 [REPORT] - Total failed: {total_failed}")
        self.log(f"📊 [REPORT] - Overall success rate: {overall_success_rate:.1f}%")

        # Best and worst performing bots
        if results:
            best_bot = max(results.items(), key=lambda x: (x[1]["success"] / (x[1]["success"] + x[1]["failed"])) if (x[1]["success"] + x[1]["failed"]) > 0 else 0)
            worst_bot = min(results.items(), key=lambda x: (x[1]["success"] / (x[1]["success"] + x[1]["failed"])) if (x[1]["success"] + x[1]["failed"]) > 0 else 0)

            best_rate = (best_bot[1]["success"] / (best_bot[1]["success"] + best_bot[1]["failed"]) * 100) if (best_bot[1]["success"] + best_bot[1]["failed"]) > 0 else 0
            worst_rate = (worst_bot[1]["success"] / (worst_bot[1]["success"] + worst_bot[1]["failed"]) * 100) if (worst_bot[1]["success"] + worst_bot[1]["failed"]) > 0 else 0

            self.log(f"🏆 [REPORT] Best performing bot: {best_bot[0]} ({best_rate:.1f}% success)")
            self.log(f"⚠️ [REPORT] Worst performing bot: {worst_bot[0]} ({worst_rate:.1f}% success)")

        # Show summary popup
        messagebox.showinfo("Multi-Bot Test Complete",
                           f"Multi-channel bot testing completed!\n\n"
                           f"Channels tested: {total_channels_tested}\n"
                           f"Total messages: {total_messages_sent}\n"
                           f"Successful: {total_successful}\n"
                           f"Failed: {total_failed}\n"
                           f"Overall success rate: {overall_success_rate:.1f}%\n\n"
                           f"Check the Logs tab for detailed results.")

    def send_to_selected_channels(self):
        """Allow user to select specific channels to test"""
        if not self.channels:
            messagebox.showinfo("Send to Selected", "No channels configured")
            return

        # Create channel selection dialog
        selection_window = tk.Toplevel(self.root)
        selection_window.title("Select Channels to Test")
        selection_window.geometry("400x300")

        ttk.Label(selection_window, text="Select channels to test:").pack(pady=10)

        # Create checkboxes for each channel
        channel_vars = {}
        for channel in self.channels:
            var = tk.BooleanVar()
            ttk.Checkbutton(selection_window, text=f"{channel['name']} ({channel['type']})",
                           variable=var).pack(anchor=tk.W, padx=20, pady=2)
            channel_vars[channel['name']] = var

        def start_selected_test():
            selected_channels = [name for name, var in channel_vars.items() if var.get()]
            if not selected_channels:
                messagebox.showinfo("No Selection", "Please select at least one channel")
                return

            selection_window.destroy()
            self._test_selected_channels(selected_channels)

        ttk.Button(selection_window, text="Start Test", command=start_selected_test).pack(pady=20)
        ttk.Button(selection_window, text="Cancel", command=selection_window.destroy).pack()

    def _test_selected_channels(self, selected_channel_names):
        """Test only the selected channels"""
        selected_channels = [ch for ch in self.channels if ch['name'] in selected_channel_names]

        if not messagebox.askyesno("Test Selected Channels",
                                  f"Test {len(selected_channels)} selected channels with {len(self.recipients)} recipients each?\n\n"
                                  f"Total messages: {len(selected_channels) * len(self.recipients)}"):
            return

        # Start testing in a separate thread
        threading.Thread(target=lambda: self._test_specific_channels_thread(selected_channels), daemon=True).start()

    def _test_specific_channels_thread(self, channels_to_test):
        """Test specific channels (similar to test_all but with selected channels)"""
        self.log(f"🚀 ========== SELECTED CHANNELS BOT TESTING STARTED ==========")
        self.log(f"🎯 [SELECTED] Testing {len(channels_to_test)} selected channels")

        # Use the same logic as _test_all_channels_thread but with selected channels
        original_active_channel = self.active_channel.get()
        overall_results = {}

        for i, channel in enumerate(channels_to_test):
            channel_name = channel["name"]
            channel_type = channel["type"]

            self.log(f"🤖 [SELECTED] ===== Testing Bot {i+1}/{len(channels_to_test)}: {channel_name} ({channel_type}) =====")

            # Set this channel as active
            self.active_channel.set(channel_name)
            self.status_var.set(f"Testing selected channel {i+1}/{len(channels_to_test)}: {channel_name}")

            # Track results for this channel
            channel_results = {
                "name": channel_name,
                "type": channel_type,
                "success": 0,
                "failed": 0,
                "messages": []
            }

            # Send all recipients to this channel
            for j, recipient in enumerate(self.recipients):
                message = recipient["message"]

                # Get identifier based on channel type
                if channel_type == "whatsapp":
                    if "phone_number" in recipient:
                        identifier = recipient["phone_number"]
                    else:
                        self.log(f"❌ [SELECTED] Skipping recipient {j+1} for {channel_name}: No phone number")
                        channel_results["failed"] += 1
                        continue
                else:  # sunco
                    if "conversation_id" in recipient:
                        identifier = recipient["conversation_id"]
                    elif "phone_number" in recipient:
                        identifier = recipient["phone_number"]
                    else:
                        self.log(f"❌ [SELECTED] Skipping recipient {j+1} for {channel_name}: No identifier")
                        channel_results["failed"] += 1
                        continue

                # Send message
                result, success = self.send_message(identifier, message)

                if success:
                    channel_results["success"] += 1
                else:
                    channel_results["failed"] += 1

                # Store message result
                channel_results["messages"].append({
                    "recipient": identifier,
                    "message": message[:50] + "..." if len(message) > 50 else message,
                    "success": success,
                    "result": result
                })

                # Small delay between messages
                if j < len(self.recipients) - 1:
                    time.sleep(self.delay_between_messages.get())

            # Store channel results
            overall_results[channel_name] = channel_results

            # Delay between channels
            if i < len(channels_to_test) - 1:
                time.sleep(2)

        # Restore original active channel
        self.active_channel.set(original_active_channel)

        # Generate report
        self._generate_multi_bot_report(overall_results)

        self.log("🏁 [SELECTED] ========== SELECTED CHANNELS TESTING COMPLETED ==========")
        self.status_var.set("Selected channels testing completed")

    def log(self, message):
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)  # Scroll to the end

if __name__ == "__main__":
    root = tk.Tk()
    app = WhatsAppBulkSender(root)
    root.mainloop()
