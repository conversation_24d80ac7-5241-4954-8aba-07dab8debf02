# WhatsApp Bulk Message Sender

A comprehensive GUI application for safely sending bulk WhatsApp messages and testing JSON-configured bots using either the WhatsApp Business API or the Sunco API (formerly Smooch).

## Features

### Multiple API Support
- WhatsApp Business API v21.0 (modern features: flows, templates, carousels)
- Sunco API (using conversation IDs)

### Channel Management
- Create and manage multiple channels with different credentials
- Test channel connections before sending
- Switch between channels easily

### Safety Features
- Test mode for safe testing without sending actual messages
- API usage monitoring and limits
- Rate limiting controls to prevent throttling
- Comprehensive error handling

### User-Friendly Interface
- Intuitive tabbed interface
- Easy recipient management
- Detailed logging and statistics
- Import/export capabilities for recipients and message history

### Advanced Features
- Phone number validation
- Customizable rate limiting
- Message history tracking
- Daily API usage tracking

### JSON Bot Testing
- Load bot test configurations from JSON files
- Smart adaptive bot testing with response detection
- Configurable test scenarios and expected responses
- Comprehensive bot performance reporting
- Support for any bot type through JSON configuration

### Modern WhatsApp Business API v21.0 Features
- Interactive Button Messages (up to 3 buttons)
- Interactive List Messages (up to 10 options)
- WhatsApp Flows (multi-step interactive experiences)
- Message Templates (pre-approved notifications)
- Carousel Messages (horizontal scrollable cards)
- Location Messages (GPS coordinates and addresses)
- Contact Messages (vCard format)
- Rich Media Support (images, videos, documents)

## Requirements

- Python 3.6 or higher
- Tkinter (usually comes with Python)
- Requests library (`pip install requests`)

## How to Use

### Initial Setup

1. Run the application:
   ```
   python whatsapp_bulk_sender.py
   ```

2. Configure your API credentials:
   - Go to the "API Configuration" tab
   - Select either "WhatsApp Business API" or "Sunco API"
   - Enter the required credentials
   - Click "Save Configuration"

### Channel Management

1. Set up channels (optional):
   - Go to the "Channels" tab
   - Add channels with different credentials for different WhatsApp numbers
   - Test connections to ensure they're working
   - Set your preferred channel as active

### Configure Settings

1. Adjust application settings:
   - Go to the "Settings" tab
   - Enable/disable test mode
   - Configure rate limiting
   - Set daily API usage limits

### Managing Recipients

1. Add recipients:
   - Go to the "Recipients" tab
   - Enter phone numbers in international format (e.g., +**********) or numbers only (e.g., **********)
   - Add custom messages for each recipient
   - Import recipients from JSON files if needed

### Sending Messages

1. Prepare for sending:
   - Ensure test mode is enabled for initial testing
   - Verify the active channel (if using channels)
   - Check rate limiting settings

2. Send messages:
   - Click "Send Messages" button
   - Confirm the action
   - Monitor progress in the "Logs" tab

3. Review results:
   - Check the "Statistics" tab for message history
   - Export message history for record-keeping

### JSON Bot Testing

1. Create or load bot configuration:
   - Use "Create Sample Config" to generate a template JSON file
   - Or load an existing JSON bot configuration file
   - The JSON should contain test scenarios, expected responses, and settings

2. Load test scenarios:
   - Click "Load JSON Config" to import bot test configuration
   - Test messages will be automatically loaded into the Recipients tab
   - Review and modify test scenarios as needed

3. Run intelligent bot tests:
   - Use "Smart JSON Bot Test" for adaptive testing based on your JSON config
   - Monitor bot responses and conversation flow completion
   - Review detailed test reports in the Logs tab

4. Export and share configurations:
   - Export current test messages to JSON format
   - Share bot configurations with team members
   - Version control your bot test scenarios

### Modern WhatsApp Messages

1. Create Interactive Messages:
   - Go to the "Modern Messages" tab
   - Select message type (buttons, lists, flows, templates, etc.)
   - Use the creation wizards to build interactive messages
   - Messages are automatically formatted for WhatsApp Business API v21.0

2. Interactive Button Messages:
   - Create messages with up to 3 action buttons
   - Perfect for quick user responses and menu navigation
   - Buttons can trigger different conversation flows

3. Interactive List Messages:
   - Create selection lists with up to 10 options
   - Great for product catalogs, service menus, or FAQ topics
   - Users can select from organized options

4. Template Messages:
   - Use pre-approved WhatsApp message templates
   - Required for sending notifications outside 24-hour window
   - Support dynamic parameters and multiple languages

5. Location and Contact Messages:
   - Share precise GPS coordinates and addresses
   - Send contact information in vCard format
   - Perfect for business locations and contact sharing

## Precautionary Steps for Live Testing

- **Test Mode**: Always use test mode first to verify everything works without sending actual messages
- **Start Small**: Begin with a small batch of recipients before scaling up
- **Monitor API Usage**: Keep track of your usage to avoid hitting API limits
- **Check Logs**: Review logs for any errors or issues

## API Configuration

### WhatsApp Business API v21.0 (Cloud API)
- Requires a **Phone Number ID** and **Access Token** from your Meta Developer account
- **Business Account ID** required for templates and flows
- **Webhook Verify Token** for receiving message responses (optional)
- Uses the latest Cloud API v21.0 with modern interactive features
- Supports text, interactive buttons, lists, flows, templates, carousels, and media

### Sunco API
- Requires only an **App ID** and **API Key** from your Sunco account
- **No Phone Number ID needed** - Sunco manages the phone number routing internally
- Uses conversation IDs to identify recipients

## Importing and Exporting

### Recipient Format
```json
[
  {
    "phone_number": "+**********",
    "message": "Hello from Support!"
  },
  {
    "phone_number": "+**********",
    "message": "Hi! How can we assist you today?"
  }
]
```

### JSON Bot Configuration Format
```json
{
  "bot_name": "Customer Service Bot",
  "bot_type": "customer_service",
  "description": "Bot configuration for testing",
  "test_scenarios": [
    {
      "name": "Welcome Flow",
      "description": "Test bot welcome responses",
      "expected_keywords": ["welcome", "hello", "menu"],
      "messages": [
        {
          "text": "hi",
          "description": "Test welcome trigger",
          "wait_time": 2
        }
      ]
    }
  ],
  "expected_responses": {
    "welcome_keywords": ["welcome", "hello", "menu"]
  },
  "test_settings": {
    "default_wait_time": 2,
    "response_timeout": 10,
    "max_conversation_depth": 20
  }
}
```

### Message History
- Export message history as CSV files
- Includes timestamps, recipients, status, and test mode information

## Notes

- The application saves all configuration locally in a file named `config.json`
- Test mode is enabled by default for safety
- Phone numbers can be in international format with + (e.g., +**********) or numbers only (e.g., **********)
- API usage counters reset daily automatically
