# WhatsApp Bulk Message Sender

A comprehensive GUI application for safely sending bulk WhatsApp messages using either the WhatsApp Business API or the Sunco API (formerly Smooch).

## Features

### Multiple API Support
- WhatsApp Business API (using phone numbers directly)
- Sunco API (using conversation IDs)

### Channel Management
- Create and manage multiple channels with different credentials
- Test channel connections before sending
- Switch between channels easily

### Safety Features
- Test mode for safe testing without sending actual messages
- API usage monitoring and limits
- Rate limiting controls to prevent throttling
- Comprehensive error handling

### User-Friendly Interface
- Intuitive tabbed interface
- Easy recipient management
- Detailed logging and statistics
- Import/export capabilities for recipients and message history

### Advanced Features
- Phone number validation
- Customizable rate limiting
- Message history tracking
- Daily API usage tracking

## Requirements

- Python 3.6 or higher
- Tkinter (usually comes with Python)
- Requests library (`pip install requests`)

## How to Use

### Initial Setup

1. Run the application:
   ```
   python whatsapp_bulk_sender.py
   ```

2. Configure your API credentials:
   - Go to the "API Configuration" tab
   - Select either "WhatsApp Business API" or "Sunco API"
   - Enter the required credentials
   - Click "Save Configuration"

### Channel Management

1. Set up channels (optional):
   - Go to the "Channels" tab
   - Add channels with different credentials for different WhatsApp numbers
   - Test connections to ensure they're working
   - Set your preferred channel as active

### Configure Settings

1. Adjust application settings:
   - Go to the "Settings" tab
   - Enable/disable test mode
   - Configure rate limiting
   - Set daily API usage limits

### Managing Recipients

1. Add recipients:
   - Go to the "Recipients" tab
   - Enter phone numbers in international format (e.g., +**********) or numbers only (e.g., **********)
   - Add custom messages for each recipient
   - Import recipients from JSON files if needed

### Sending Messages

1. Prepare for sending:
   - Ensure test mode is enabled for initial testing
   - Verify the active channel (if using channels)
   - Check rate limiting settings

2. Send messages:
   - Click "Send Messages" button
   - Confirm the action
   - Monitor progress in the "Logs" tab

3. Review results:
   - Check the "Statistics" tab for message history
   - Export message history for record-keeping

## Precautionary Steps for Live Testing

- **Test Mode**: Always use test mode first to verify everything works without sending actual messages
- **Start Small**: Begin with a small batch of recipients before scaling up
- **Monitor API Usage**: Keep track of your usage to avoid hitting API limits
- **Check Logs**: Review logs for any errors or issues

## API Configuration

### WhatsApp Business API (Cloud API)
- Requires a **Phone Number ID** and **Access Token** from your Meta Developer account
- Uses the Cloud API to send messages directly to phone numbers
- Phone Number ID is specific to each WhatsApp Business phone number

### Sunco API
- Requires only an **App ID** and **API Key** from your Sunco account
- **No Phone Number ID needed** - Sunco manages the phone number routing internally
- Uses conversation IDs to identify recipients

## Importing and Exporting

### Recipient Format
```json
[
  {
    "phone_number": "+**********",
    "message": "Hello from Support!"
  },
  {
    "phone_number": "+**********",
    "message": "Hi! How can we assist you today?"
  }
]
```

### Message History
- Export message history as CSV files
- Includes timestamps, recipients, status, and test mode information

## Notes

- The application saves all configuration locally in a file named `config.json`
- Test mode is enabled by default for safety
- Phone numbers can be in international format with + (e.g., +**********) or numbers only (e.g., **********)
- API usage counters reset daily automatically
